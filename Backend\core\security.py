"""
نظام الأمان والمصادقة
Security and Authentication System
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from database.config import get_db
from models.users.user import User
import os

# إعدادات الأمان
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# إعداد Bearer Token
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    التحقق من كلمة المرور
    Verify password against hash
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    تشفير كلمة المرور
    Hash password
    """
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    إنشاء رمز الوصول
    Create access token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    التحقق من صحة الرمز المميز
    Verify token validity
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except JWTError:
        return None

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    مصادقة المستخدم
    Authenticate user
    """
    user = db.query(User).filter(User.username == username).first()
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    الحصول على المستخدم الحالي من الرمز المميز
    Get current user from token
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    الحصول على المستخدم النشط الحالي
    Get current active user
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """
    الحصول على المستخدم الإداري الحالي
    Get current admin user
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

def check_permission(user: User, module: str, action: str) -> bool:
    """
    التحقق من صلاحية المستخدم
    Check user permission
    """
    # إذا كان المستخدم مدير، فله جميع الصلاحيات
    if user.is_admin:
        return True
    
    # التحقق من الصلاحيات (سيتم تطويرها لاحقاً مع نظام الأدوار)
    # TODO: Implement role-based permission checking
    return True

class PermissionChecker:
    """
    فئة للتحقق من الصلاحيات
    Permission checker class
    """
    
    def __init__(self, module: str, action: str):
        self.module = module
        self.action = action
    
    def __call__(self, current_user: User = Depends(get_current_active_user)) -> User:
        if not check_permission(current_user, self.module, self.action):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Not enough permissions for {self.module}:{self.action}"
            )
        return current_user

# مولدات الصلاحيات للوحدات المختلفة
def require_accounting_read():
    return PermissionChecker("accounting", "read")

def require_accounting_write():
    return PermissionChecker("accounting", "write")

def require_sales_read():
    return PermissionChecker("sales", "read")

def require_sales_write():
    return PermissionChecker("sales", "write")

def require_purchases_read():
    return PermissionChecker("purchases", "read")

def require_purchases_write():
    return PermissionChecker("purchases", "write")

def require_inventory_read():
    return PermissionChecker("inventory", "read")

def require_inventory_write():
    return PermissionChecker("inventory", "write")

def require_financial_read():
    return PermissionChecker("financial", "read")

def require_financial_write():
    return PermissionChecker("financial", "write")

def require_reports_read():
    return PermissionChecker("reports", "read")
