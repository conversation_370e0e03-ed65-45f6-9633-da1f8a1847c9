using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Accounting
{
    public partial class ChartOfAccountsWindow : Window
    {
        private ObservableCollection<AccountViewModel> _accounts;
        private AccountViewModel _selectedAccount;

        public ChartOfAccountsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _accounts = new ObservableCollection<AccountViewModel>
            {
                new AccountViewModel
                {
                    AccountNumber = "1000",
                    Name = "Assets",
                    NameAr = "الأصول",
                    Balance = 0,
                    IsParent = true,
                    Children = new ObservableCollection<AccountViewModel>
                    {
                        new AccountViewModel
                        {
                            AccountNumber = "1100",
                            Name = "Current Assets",
                            NameAr = "الأصول المتداولة",
                            Balance = 150000,
                            IsParent = true,
                            Children = new ObservableCollection<AccountViewModel>
                            {
                                new AccountViewModel { AccountNumber = "1101", Name = "Cash", NameAr = "النقدية", Balance = 50000 },
                                new AccountViewModel { AccountNumber = "1102", Name = "Bank", NameAr = "البنك", Balance = 75000 },
                                new AccountViewModel { AccountNumber = "1103", Name = "Accounts Receivable", NameAr = "العملاء", Balance = 25000 }
                            }
                        },
                        new AccountViewModel
                        {
                            AccountNumber = "1200",
                            Name = "Fixed Assets",
                            NameAr = "الأصول الثابتة",
                            Balance = 200000,
                            IsParent = true,
                            Children = new ObservableCollection<AccountViewModel>
                            {
                                new AccountViewModel { AccountNumber = "1201", Name = "Equipment", NameAr = "المعدات", Balance = 100000 },
                                new AccountViewModel { AccountNumber = "1202", Name = "Furniture", NameAr = "الأثاث", Balance = 50000 },
                                new AccountViewModel { AccountNumber = "1203", Name = "Vehicles", NameAr = "المركبات", Balance = 50000 }
                            }
                        }
                    }
                },
                new AccountViewModel
                {
                    AccountNumber = "2000",
                    Name = "Liabilities",
                    NameAr = "الخصوم",
                    Balance = 0,
                    IsParent = true,
                    Children = new ObservableCollection<AccountViewModel>
                    {
                        new AccountViewModel
                        {
                            AccountNumber = "2100",
                            Name = "Current Liabilities",
                            NameAr = "الخصوم المتداولة",
                            Balance = 80000,
                            IsParent = true,
                            Children = new ObservableCollection<AccountViewModel>
                            {
                                new AccountViewModel { AccountNumber = "2101", Name = "Accounts Payable", NameAr = "الموردين", Balance = 30000 },
                                new AccountViewModel { AccountNumber = "2102", Name = "Accrued Expenses", NameAr = "المصروفات المستحقة", Balance = 20000 },
                                new AccountViewModel { AccountNumber = "2103", Name = "Short-term Loans", NameAr = "القروض قصيرة الأجل", Balance = 30000 }
                            }
                        }
                    }
                },
                new AccountViewModel
                {
                    AccountNumber = "3000",
                    Name = "Equity",
                    NameAr = "حقوق الملكية",
                    Balance = 270000,
                    IsParent = true,
                    Children = new ObservableCollection<AccountViewModel>
                    {
                        new AccountViewModel { AccountNumber = "3001", Name = "Capital", NameAr = "رأس المال", Balance = 200000 },
                        new AccountViewModel { AccountNumber = "3002", Name = "Retained Earnings", NameAr = "الأرباح المحتجزة", Balance = 70000 }
                    }
                }
            };

            // حساب الأرصدة للحسابات الأب
            CalculateParentBalances(_accounts);
            
            AccountsTreeView.ItemsSource = _accounts;
            UpdateAccountCount();
        }

        private void CalculateParentBalances(ObservableCollection<AccountViewModel> accounts)
        {
            foreach (var account in accounts)
            {
                if (account.IsParent && account.Children?.Any() == true)
                {
                    CalculateParentBalances(account.Children);
                    account.Balance = account.Children.Sum(c => c.Balance);
                }
            }
        }

        private void UpdateAccountCount()
        {
            var totalCount = CountAccounts(_accounts);
            AccountCountTextBlock.Text = $"عدد الحسابات: {totalCount}";
        }

        private int CountAccounts(ObservableCollection<AccountViewModel> accounts)
        {
            int count = accounts.Count;
            foreach (var account in accounts)
            {
                if (account.Children?.Any() == true)
                {
                    count += CountAccounts(account.Children);
                }
            }
            return count;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddAccount_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddAccountWindow(_selectedAccount);
            if (addWindow.ShowDialog() == true)
            {
                // إضافة الحساب الجديد
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة الحساب بنجاح";
            }
        }

        private void EditAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddAccountWindow(_selectedAccount, isEdit: true);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل الحساب بنجاح";
            }
        }

        private void DeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الحساب '{_selectedAccount.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // حذف الحساب
                StatusTextBlock.Text = "تم حذف الحساب بنجاح";
                RefreshButton_Click(sender, e);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                AccountsTreeView.ItemsSource = _accounts;
            }
            else
            {
                var filteredAccounts = FilterAccounts(_accounts, searchText);
                AccountsTreeView.ItemsSource = filteredAccounts;
            }
        }

        private ObservableCollection<AccountViewModel> FilterAccounts(ObservableCollection<AccountViewModel> accounts, string searchText)
        {
            var filtered = new ObservableCollection<AccountViewModel>();
            
            foreach (var account in accounts)
            {
                if (account.Name.ToLower().Contains(searchText) || 
                    account.NameAr.Contains(searchText) || 
                    account.AccountNumber.Contains(searchText))
                {
                    filtered.Add(account);
                }
                else if (account.Children?.Any() == true)
                {
                    var filteredChildren = FilterAccounts(account.Children, searchText);
                    if (filteredChildren.Any())
                    {
                        var parentCopy = new AccountViewModel
                        {
                            AccountNumber = account.AccountNumber,
                            Name = account.Name,
                            NameAr = account.NameAr,
                            Balance = account.Balance,
                            IsParent = account.IsParent,
                            Children = filteredChildren
                        };
                        filtered.Add(parentCopy);
                    }
                }
            }
            
            return filtered;
        }

        private void AccountsTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            _selectedAccount = e.NewValue as AccountViewModel;
            
            if (_selectedAccount != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedAccount.AccountNumber} - {_selectedAccount.Name}";
            }
        }
    }

    public class AccountViewModel
    {
        public string AccountNumber { get; set; }
        public string Name { get; set; }
        public string NameAr { get; set; }
        public decimal Balance { get; set; }
        public bool IsParent { get; set; }
        public ObservableCollection<AccountViewModel> Children { get; set; }
        
        public string BalanceColor => Balance >= 0 ? "Green" : "Red";
    }
}
