using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Finance
{
    public partial class BankTransferWindow : Window
    {
        public BankTransferWindow()
        {
            InitializeComponent();
        }

        private void ExecuteButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم تنفيذ التحويل البنكي
                var amount = decimal.Parse(AmountTextBox.Text);
                var fees = decimal.Parse(FeesTextBox.Text);
                var description = DescriptionTextBox.Text.Trim();
                var reference = ReferenceTextBox.Text.Trim();
                var transferDate = TransferDatePicker.SelectedDate ?? DateTime.Today;

                var fromAccount = FromAccountComboBox.Text;
                var toAccount = ToAccountComboBox.Text;

                // محاكاة تنفيذ التحويل
                var message = $"تم تنفيذ التحويل بمبلغ {amount:C} من {fromAccount} إلى {toAccount} بنجاح!";
                if (fees > 0)
                {
                    message += $"\nرسوم التحويل: {fees:C}";
                }
                
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تنفيذ التحويل: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من نوع التحويل
            if (TransferTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع التحويل");
                TransferTypeComboBox.Focus();
                return false;
            }

            // التحقق من الحساب المرسل
            if (FromAccountComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الحساب المرسل");
                FromAccountComboBox.Focus();
                return false;
            }

            // التحقق من الحساب المستقبل
            if (ToAccountComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الحساب المستقبل");
                ToAccountComboBox.Focus();
                return false;
            }

            // التحقق من عدم تطابق الحسابات
            if (FromAccountComboBox.SelectedItem == ToAccountComboBox.SelectedItem)
            {
                ShowError("لا يمكن أن يكون الحساب المرسل والمستقبل نفس الحساب");
                ToAccountComboBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            {
                ShowError("يرجى إدخال مبلغ صحيح أكبر من الصفر");
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من الرسوم
            if (!decimal.TryParse(FeesTextBox.Text, out var fees) || fees < 0)
            {
                ShowError("يرجى إدخال رسوم صحيحة");
                FeesTextBox.Focus();
                return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                ShowError("يرجى إدخال وصف التحويل");
                DescriptionTextBox.Focus();
                return false;
            }

            // التحقق من التاريخ
            if (TransferDatePicker.SelectedDate == null)
            {
                ShowError("يرجى اختيار تاريخ التحويل");
                TransferDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
