@echo off
echo ========================================
echo    نظام المحاسبة المالية المتكامل
echo    Integrated Financial Accounting System
echo ========================================
echo.

echo 🔧 بدء تشغيل النظام...
echo 🔧 Starting the system...
echo.

echo 📊 تشغيل Backend (Python FastAPI)...
echo 📊 Starting Backend (Python FastAPI)...
cd Backend
start "Backend Server" cmd /k "python run_server.py"
cd ..

echo ⏳ انتظار تشغيل Backend...
echo ⏳ Waiting for Backend to start...
timeout /t 5 /nobreak > nul

echo 🖥️  تشغيل Frontend (C# WPF)...
echo 🖥️  Starting Frontend (C# WPF)...
cd Frontend\AccountingApp.WPF
start "Frontend App" cmd /k "dotnet run"
cd ..\..

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo ✅ System started successfully!
echo.
echo 📝 معلومات تسجيل الدخول:
echo 📝 Login credentials:
echo    اسم المستخدم / Username: admin
echo    كلمة المرور / Password: admin123
echo.
echo 🌐 Backend API: http://127.0.0.1:8000
echo 📖 API Docs: http://127.0.0.1:8000/docs
echo.
pause
