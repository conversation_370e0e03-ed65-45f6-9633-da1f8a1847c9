using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Newtonsoft.Json;

namespace AccountingApp.WPF.Views
{
    public partial class SimpleLoginWindow : Window
    {
        private readonly HttpClient _httpClient;

        public SimpleLoginWindow()
        {
            InitializeComponent();
            _httpClient = new HttpClient();

            // تعيين كلمة المرور الافتراضية
            PasswordBox.Password = "admin123";

            // التركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ErrorTextBlock.Visibility = Visibility.Collapsed;

                var username = UsernameTextBox.Text;
                var password = PasswordBox.Password;

                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    return;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                // محاكاة تأخير تسجيل الدخول
                await Task.Delay(500);

                // التحقق من بيانات الاعتماد المحددة مسبقاً (نظام محلي)
                if ((username == "admin" && password == "admin123") ||
                    (username == "user" && password == "user123") ||
                    (username == "مدير" && password == "123456"))
                {
                    // تسجيل الدخول نجح
                    MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إنشاء النافذة الرئيسية
                    var mainWindow = new SimpleMainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    // فشل تسجيل الدخول
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        protected override void OnClosed(EventArgs e)
        {
            _httpClient?.Dispose();
            base.OnClosed(e);
        }
    }
}
