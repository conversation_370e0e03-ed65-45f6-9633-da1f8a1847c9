using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Accounting
{
    public partial class AddJournalEntryWindow : Window
    {
        private readonly JournalEntryViewModel _editEntry;
        private readonly bool _isEdit;
        private ObservableCollection<JournalEntryLineViewModel> _entryLines;

        public ObservableCollection<AccountItem> Accounts { get; set; }

        public AddJournalEntryWindow(JournalEntryViewModel editEntry = null)
        {
            InitializeComponent();
            _editEntry = editEntry;
            _isEdit = editEntry != null;

            LoadAccounts();
            InitializeEntryLines();
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل قيد يومي - Edit Journal Entry";
                SaveButton.Content = "تحديث";
                LoadEntryData();
            }
            else
            {
                GenerateEntryNumber();
            }

            DataContext = this;
        }

        private void LoadAccounts()
        {
            Accounts = new ObservableCollection<AccountItem>
            {
                new AccountItem { AccountNumber = "1101", DisplayName = "1101 - النقدية (Cash)" },
                new AccountItem { AccountNumber = "1102", DisplayName = "1102 - البنك (Bank)" },
                new AccountItem { AccountNumber = "1103", DisplayName = "1103 - العملاء (Accounts Receivable)" },
                new AccountItem { AccountNumber = "1201", DisplayName = "1201 - المعدات (Equipment)" },
                new AccountItem { AccountNumber = "1202", DisplayName = "1202 - الأثاث (Furniture)" },
                new AccountItem { AccountNumber = "1203", DisplayName = "1203 - المركبات (Vehicles)" },
                new AccountItem { AccountNumber = "2101", DisplayName = "2101 - الموردين (Accounts Payable)" },
                new AccountItem { AccountNumber = "2102", DisplayName = "2102 - المصروفات المستحقة (Accrued Expenses)" },
                new AccountItem { AccountNumber = "2103", DisplayName = "2103 - القروض قصيرة الأجل (Short-term Loans)" },
                new AccountItem { AccountNumber = "3001", DisplayName = "3001 - رأس المال (Capital)" },
                new AccountItem { AccountNumber = "3002", DisplayName = "3002 - الأرباح المحتجزة (Retained Earnings)" },
                new AccountItem { AccountNumber = "4001", DisplayName = "4001 - إيرادات المبيعات (Sales Revenue)" },
                new AccountItem { AccountNumber = "5001", DisplayName = "5001 - تكلفة البضاعة المباعة (Cost of Goods Sold)" },
                new AccountItem { AccountNumber = "5002", DisplayName = "5002 - مصروفات الإيجار (Rent Expense)" },
                new AccountItem { AccountNumber = "5003", DisplayName = "5003 - مصروفات الكهرباء (Utilities Expense)" }
            };
        }

        private void InitializeEntryLines()
        {
            _entryLines = new ObservableCollection<JournalEntryLineViewModel>();
            
            // إضافة سطرين فارغين للبداية
            _entryLines.Add(new JournalEntryLineViewModel());
            _entryLines.Add(new JournalEntryLineViewModel());
            
            EntryLinesDataGrid.ItemsSource = _entryLines;
            
            // ربط أحداث تغيير البيانات
            _entryLines.CollectionChanged += (s, e) => UpdateTotals();
        }

        private void GenerateEntryNumber()
        {
            // توليد رقم قيد جديد
            var today = DateTime.Today;
            var entryNumber = $"JE{today:yyyyMMdd}{new Random().Next(100, 999)}";
            EntryNumberTextBox.Text = entryNumber;
        }

        private void LoadEntryData()
        {
            if (_editEntry == null) return;

            EntryNumberTextBox.Text = _editEntry.EntryNumber;
            EntryDatePicker.SelectedDate = _editEntry.EntryDate;
            ReferenceTextBox.Text = _editEntry.Reference;
            DescriptionTextBox.Text = _editEntry.Description;
        }

        private void AddLine_Click(object sender, RoutedEventArgs e)
        {
            _entryLines.Add(new JournalEntryLineViewModel());
        }

        private void DeleteLine_Click(object sender, RoutedEventArgs e)
        {
            var selectedLine = EntryLinesDataGrid.SelectedItem as JournalEntryLineViewModel;
            if (selectedLine != null && _entryLines.Count > 2)
            {
                _entryLines.Remove(selectedLine);
            }
            else if (_entryLines.Count <= 2)
            {
                MessageBox.Show("يجب أن يحتوي القيد على سطرين على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void UpdateTotals()
        {
            var totalDebit = _entryLines.Sum(l => l.DebitAmount);
            var totalCredit = _entryLines.Sum(l => l.CreditAmount);

            TotalDebitTextBlock.Text = totalDebit.ToString("F2");
            TotalCreditTextBlock.Text = totalCredit.ToString("F2");

            // تغيير لون النص حسب التوازن
            if (totalDebit == totalCredit && totalDebit > 0)
            {
                TotalDebitTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                TotalCreditTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                ErrorTextBlock.Visibility = Visibility.Collapsed;
            }
            else
            {
                TotalDebitTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                TotalCreditTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                
                if (totalDebit != totalCredit)
                {
                    ErrorTextBlock.Text = "القيد غير متوازن - يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن";
                    ErrorTextBlock.Visibility = Visibility.Visible;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateEntry())
            {
                SaveEntry(false);
            }
        }

        private void SaveAndPostButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateEntry())
            {
                SaveEntry(true);
            }
        }

        private bool ValidateEntry()
        {
            // التحقق من البيانات الأساسية
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                ShowError("يرجى إدخال وصف القيد");
                DescriptionTextBox.Focus();
                return false;
            }

            if (EntryDatePicker.SelectedDate == null)
            {
                ShowError("يرجى اختيار تاريخ القيد");
                EntryDatePicker.Focus();
                return false;
            }

            // التحقق من الأسطر
            var validLines = _entryLines.Where(l => !string.IsNullOrWhiteSpace(l.AccountId) && 
                                                   (l.DebitAmount > 0 || l.CreditAmount > 0)).ToList();

            if (validLines.Count < 2)
            {
                ShowError("يجب أن يحتوي القيد على سطرين صحيحين على الأقل");
                return false;
            }

            // التحقق من التوازن
            var totalDebit = validLines.Sum(l => l.DebitAmount);
            var totalCredit = validLines.Sum(l => l.CreditAmount);

            if (totalDebit != totalCredit)
            {
                ShowError("القيد غير متوازن - يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن");
                return false;
            }

            if (totalDebit == 0)
            {
                ShowError("يجب أن يحتوي القيد على مبالغ");
                return false;
            }

            return true;
        }

        private void SaveEntry(bool andPost)
        {
            try
            {
                // هنا يتم حفظ القيد في قاعدة البيانات
                var message = _isEdit ? "تم تحديث القيد بنجاح!" : "تم إضافة القيد بنجاح!";
                
                if (andPost)
                {
                    message += " وتم ترحيله.";
                }

                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ القيد: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class JournalEntryLineViewModel
    {
        public string AccountId { get; set; }
        public string Description { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
    }

    public class AccountItem
    {
        public string AccountNumber { get; set; }
        public string DisplayName { get; set; }
    }
}
