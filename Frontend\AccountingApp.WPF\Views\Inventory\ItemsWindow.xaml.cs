using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class ItemsWindow : Window
    {
        private ObservableCollection<ItemViewModel> _items;
        private ItemViewModel _selectedItem;

        public ItemsWindow()
        {
            try
            {
                InitializeComponent();
                LoadSampleData();
                LoadCategories();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الأصناف:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Error in ItemsWindow constructor: {ex}");
            }
        }

        private void LoadSampleData()
        {
            try
            {
                _items = new ObservableCollection<ItemViewModel>
            {
                new ItemViewModel
                {
                    ItemNumber = "ITM001",
                    Name = "Laptop Dell Inspiron",
                    NameAr = "لابتوب ديل انسبايرون",
                    Barcode = "1234567890123",
                    CategoryName = "إلكترونيات",
                    UnitName = "قطعة",
                    ItemType = "Product",
                    PurchasePrice = 2500,
                    SellingPrice = 3200,
                    AvailableQuantity = 15,
                    MinimumStock = 5,
                    IsActive = true
                },
                new ItemViewModel
                {
                    ItemNumber = "ITM002",
                    Name = "Office Chair",
                    NameAr = "كرسي مكتب",
                    Barcode = "2345678901234",
                    CategoryName = "أثاث",
                    UnitName = "قطعة",
                    ItemType = "Product",
                    PurchasePrice = 150,
                    SellingPrice = 220,
                    AvailableQuantity = 8,
                    MinimumStock = 10,
                    IsActive = true
                },
                new ItemViewModel
                {
                    ItemNumber = "ITM003",
                    Name = "A4 Paper Pack",
                    NameAr = "حزمة ورق A4",
                    Barcode = "3456789012345",
                    CategoryName = "قرطاسية",
                    UnitName = "حزمة",
                    ItemType = "Product",
                    PurchasePrice = 25,
                    SellingPrice = 35,
                    AvailableQuantity = 50,
                    MinimumStock = 20,
                    IsActive = true
                },
                new ItemViewModel
                {
                    ItemNumber = "SRV001",
                    Name = "Technical Support",
                    NameAr = "الدعم الفني",
                    Barcode = "",
                    CategoryName = "خدمات",
                    UnitName = "ساعة",
                    ItemType = "Service",
                    PurchasePrice = 0,
                    SellingPrice = 100,
                    AvailableQuantity = 0,
                    MinimumStock = 0,
                    IsActive = true
                },
                new ItemViewModel
                {
                    ItemNumber = "ITM004",
                    Name = "Wireless Mouse",
                    NameAr = "فأرة لاسلكية",
                    Barcode = "4567890123456",
                    CategoryName = "إلكترونيات",
                    UnitName = "قطعة",
                    ItemType = "Product",
                    PurchasePrice = 15,
                    SellingPrice = 25,
                    AvailableQuantity = 2,
                    MinimumStock = 10,
                    IsActive = false
                }
            };

                ItemsDataGrid.ItemsSource = _items;
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الأصناف:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Error in LoadSampleData: {ex}");

                // إنشاء قائمة فارغة في حالة الخطأ
                _items = new ObservableCollection<ItemViewModel>();
                ItemsDataGrid.ItemsSource = _items;
            }
        }

        private void LoadCategories()
        {
            try
            {
                var categories = new[]
                {
                    "جميع الفئات",
                    "إلكترونيات",
                    "أثاث",
                    "قرطاسية",
                    "خدمات"
                };

                CategoryFilterComboBox.ItemsSource = categories;
                CategoryFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Error in LoadCategories: {ex}");
            }
        }

        private void UpdateSummary()
        {
            ItemCountTextBlock.Text = $"عدد الأصناف: {_items.Count}";
            var totalValue = _items.Where(i => i.ItemType == "Product")
                                  .Sum(i => i.AvailableQuantity * i.PurchasePrice);
            TotalValueTextBlock.Text = $"إجمالي قيمة المخزون: {totalValue:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddItem_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddItemWindow();
            if (addWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة الصنف بنجاح";
            }
        }

        private void EditItem_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedItem == null)
            {
                MessageBox.Show("يرجى اختيار صنف للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddItemWindow(_selectedItem);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل الصنف بنجاح";
            }
        }

        private void DeleteItem_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedItem == null)
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الصنف '{_selectedItem.Name}'؟",
                                       "تأكيد الحذف",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _items.Remove(_selectedItem);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف الصنف بنجاح";
            }
        }

        private void ManageCategories_Click(object sender, RoutedEventArgs e)
        {
            var categoriesWindow = new CategoriesWindow();
            categoriesWindow.ShowDialog();
        }

        private void ManageUnits_Click(object sender, RoutedEventArgs e)
        {
            var unitsWindow = new UnitsWindow();
            unitsWindow.ShowDialog();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            var selectedCategory = CategoryFilterComboBox.SelectedItem?.ToString() ?? "جميع الفئات";

            var filteredItems = _items.Where(i =>
                (string.IsNullOrWhiteSpace(searchText) ||
                 i.Name.ToLower().Contains(searchText) ||
                 i.NameAr.Contains(searchText) ||
                 i.ItemNumber.ToLower().Contains(searchText) ||
                 i.Barcode.Contains(searchText)) &&
                (selectedCategory == "جميع الفئات" || i.CategoryName == selectedCategory)
            ).ToList();

            ItemsDataGrid.ItemsSource = new ObservableCollection<ItemViewModel>(filteredItems);

            ItemCountTextBlock.Text = $"عدد الأصناف: {filteredItems.Count}";
            var totalValue = filteredItems.Where(i => i.ItemType == "Product")
                                         .Sum(i => i.AvailableQuantity * i.PurchasePrice);
            TotalValueTextBlock.Text = $"إجمالي قيمة المخزون: {totalValue:C}";
        }

        private void ItemsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedItem = ItemsDataGrid.SelectedItem as ItemViewModel;

            if (_selectedItem != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedItem.ItemNumber} - {_selectedItem.Name}";
            }
        }
    }

    public class ItemViewModel
    {
        public string ItemNumber { get; set; } = "";
        public string Name { get; set; } = "";
        public string NameAr { get; set; } = "";
        public string Barcode { get; set; } = "";
        public string CategoryName { get; set; } = "";
        public string UnitName { get; set; } = "";
        public string ItemType { get; set; } = "";
        public decimal PurchasePrice { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal AvailableQuantity { get; set; }
        public decimal MinimumStock { get; set; }
        public bool IsActive { get; set; }

        public string ItemTypeText => ItemType == "Product" ? "منتج" : "خدمة";
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
        public string StockColor => AvailableQuantity <= MinimumStock ? "Red" : "Green";
    }
}
