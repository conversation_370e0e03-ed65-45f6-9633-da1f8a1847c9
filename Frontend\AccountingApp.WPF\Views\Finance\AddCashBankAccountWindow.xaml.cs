using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Finance
{
    public partial class AddCashBankAccountWindow : Window
    {
        private readonly CashBankAccountViewModel _editAccount;
        private readonly bool _isEdit;

        public AddCashBankAccountWindow(CashBankAccountViewModel editAccount = null)
        {
            InitializeComponent();
            _editAccount = editAccount;
            _isEdit = editAccount != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل الحساب - Edit Account";
                SaveButton.Content = "تحديث";
                LoadAccountData();
            }
            else
            {
                GenerateAccountNumber();
            }
        }

        private void GenerateAccountNumber()
        {
            // توليد رقم حساب جديد
            var today = DateTime.Today;
            var accountNumber = $"ACC{today:yyyyMMdd}{new Random().Next(100, 999)}";
            AccountNumberTextBox.Text = accountNumber;
        }

        private void LoadAccountData()
        {
            if (_editAccount == null) return;

            AccountNumberTextBox.Text = _editAccount.AccountNumber;
            AccountNameTextBox.Text = _editAccount.AccountName;
            BankNameComboBox.Text = _editAccount.BankName;
            BankAccountNumberTextBox.Text = _editAccount.BankAccountNumber;
            IsActiveCheckBox.IsChecked = _editAccount.IsActive;

            // تعيين نوع الحساب
            foreach (ComboBoxItem item in AccountTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == _editAccount.AccountType)
                {
                    AccountTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين العملة
            foreach (ComboBoxItem item in CurrencyComboBox.Items)
            {
                if (item.Tag?.ToString() == _editAccount.Currency)
                {
                    CurrencyComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void AccountTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (AccountTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var accountType = selectedItem.Tag?.ToString();
                
                // إظهار/إخفاء معلومات البنك حسب نوع الحساب
                if (accountType == "Bank")
                {
                    BankInfoLabel.Visibility = Visibility.Visible;
                    BankInfoPanel.Visibility = Visibility.Visible;
                }
                else
                {
                    BankInfoLabel.Visibility = Visibility.Collapsed;
                    BankInfoPanel.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                var accountNumber = AccountNumberTextBox.Text.Trim();
                var accountName = AccountNameTextBox.Text.Trim();
                var bankName = BankNameComboBox.Text?.Trim() ?? "";
                var bankAccountNumber = BankAccountNumberTextBox.Text.Trim();
                var openingBalance = decimal.Parse(OpeningBalanceTextBox.Text);
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                var selectedType = AccountTypeComboBox.SelectedItem as ComboBoxItem;
                var accountType = selectedType?.Tag?.ToString() ?? "Cash";

                var selectedCurrency = CurrencyComboBox.SelectedItem as ComboBoxItem;
                var currency = selectedCurrency?.Tag?.ToString() ?? "SAR";

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث الحساب بنجاح!" : "تم إضافة الحساب بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من رقم الحساب
            if (string.IsNullOrWhiteSpace(AccountNumberTextBox.Text))
            {
                ShowError("يرجى إدخال رقم الحساب");
                AccountNumberTextBox.Focus();
                return false;
            }

            // التحقق من اسم الحساب
            if (string.IsNullOrWhiteSpace(AccountNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الحساب");
                AccountNameTextBox.Focus();
                return false;
            }

            // التحقق من نوع الحساب
            if (AccountTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع الحساب");
                AccountTypeComboBox.Focus();
                return false;
            }

            // التحقق من العملة
            if (CurrencyComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار العملة");
                CurrencyComboBox.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!decimal.TryParse(OpeningBalanceTextBox.Text, out _))
            {
                ShowError("يرجى إدخال رصيد افتتاحي صحيح");
                OpeningBalanceTextBox.Focus();
                return false;
            }

            // التحقق من معلومات البنك إذا كان نوع الحساب بنك
            var selectedType = AccountTypeComboBox.SelectedItem as ComboBoxItem;
            if (selectedType?.Tag?.ToString() == "Bank")
            {
                if (BankNameComboBox.SelectedItem == null)
                {
                    ShowError("يرجى اختيار اسم البنك");
                    BankNameComboBox.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(BankAccountNumberTextBox.Text))
                {
                    ShowError("يرجى إدخال رقم الحساب البنكي");
                    BankAccountNumberTextBox.Focus();
                    return false;
                }
            }

            // التحقق من عدم تكرار رقم الحساب (محاكاة)
            if (!_isEdit && IsAccountNumberExists(AccountNumberTextBox.Text.Trim()))
            {
                ShowError("رقم الحساب موجود مسبقاً، يرجى اختيار رقم آخر");
                AccountNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsAccountNumberExists(string accountNumber)
        {
            // محاكاة التحقق من وجود رقم الحساب
            var existingNumbers = new[] { "CASH001", "CASH002", "BANK001", "BANK002", "BANK003" };
            return Array.Exists(existingNumbers, x => x == accountNumber);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
