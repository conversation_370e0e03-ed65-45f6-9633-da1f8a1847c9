<Window x:Class="AccountingApp.WPF.Views.Reports.PurchasesReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير المشتريات - Purchases Report"
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#DC3545" Padding="15">
            <Grid>
                <TextBlock x:Name="HeaderTextBlock"
                          Text="تقرير المشتريات - Purchases Report"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel>
                <TextBlock Text="فلاتر التقرير:" FontWeight="SemiBold" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="1" x:Name="FromDatePicker" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="3" x:Name="ToDatePicker" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="4" Text="المورد:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="5" x:Name="SupplierFilterComboBox" Margin="0,0,20,0">
                        <ComboBoxItem Content="جميع الموردين" IsSelected="True"/>
                        <ComboBoxItem Content="شركة التوريدات المتقدمة"/>
                        <ComboBoxItem Content="مؤسسة الإمدادات الصناعية"/>
                        <ComboBoxItem Content="شركة المواد الخام"/>
                    </ComboBox>
                    
                    <Button Grid.Column="6" Content="تطبيق الفلاتر" Background="#007ACC" Foreground="White" Padding="15,5" Click="ApplyFilters_Click"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Report Data -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="PurchasesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                    <DataGridTextColumn Header="رقم فاتورة المورد" Binding="{Binding SupplierInvoiceNumber}" Width="140"/>
                    <DataGridTextColumn Header="المبلغ قبل الضريبة" Binding="{Binding SubTotal, StringFormat=F2}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Blue"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat=F2}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Orange"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="إجمالي الفاتورة" Binding="{Binding TotalAmount, StringFormat=F2}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Red"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المبلغ المدفوع" Binding="{Binding PaidAmount, StringFormat=F2}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Green"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat=F2}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="{Binding RemainingAmountColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Summary -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="عدد الفواتير" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="InvoiceCountTextBlock" Text="0" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#007ACC"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المشتريات" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalPurchasesTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Red"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الضرائب" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalTaxTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Orange"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المدفوع" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalPaidTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                </StackPanel>
                
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المتبقي" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalRemainingTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Red"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Actions -->
        <Border Grid.Row="4" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="طباعة التقرير" Background="#6C757D" Foreground="White" Padding="20,10" Margin="10" Click="PrintReport_Click"/>
                <Button Content="تصدير إلى Excel" Background="#198754" Foreground="White" Padding="20,10" Margin="10" Click="ExportToExcel_Click"/>
                <Button Content="تصدير إلى PDF" Background="#DC3545" Foreground="White" Padding="20,10" Margin="10" Click="ExportToPDF_Click"/>
                <Button Content="تحديث البيانات" Background="#0D6EFD" Foreground="White" Padding="20,10" Margin="10" Click="RefreshData_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
