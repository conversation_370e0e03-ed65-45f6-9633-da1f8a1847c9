<Window x:Class="AccountingApp.WPF.Views.Inventory.UnitsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة وحدات القياس - Units Management"
        Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#E83E8C" Padding="15">
            <Grid>
                <TextBlock Text="إدارة وحدات القياس - Units Management"
                          FontSize="16"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="وحدة جديدة"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddUnit_Click"/>
                
                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditUnit_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteUnit_Click"/>
            </StackPanel>
        </Border>

        <!-- Units DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="UnitsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="UnitsDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الوحدة" Binding="{Binding UnitCode}" Width="100"/>
                    <DataGridTextColumn Header="اسم الوحدة" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="الاسم بالعربية" Binding="{Binding NameAr}" Width="150"/>
                    <DataGridTextColumn Header="الرمز" Binding="{Binding Symbol}" Width="80"/>
                    <DataGridTextColumn Header="النوع" Binding="{Binding UnitTypeText}" Width="100"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر وحدة للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <TextBlock x:Name="UnitCountTextBlock"
                          Text="عدد الوحدات: 0"
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
