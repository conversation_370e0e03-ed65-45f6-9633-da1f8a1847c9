<Window x:Class="AccountingApp.WPF.Views.Finance.AddCashBankAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب نقدي/بنكي - Add Cash/Bank Account"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#17A2B8" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة حساب نقدي/بنكي - Add Cash/Bank Account"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="AccountNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="نوع الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="AccountTypeComboBox"
                                 Padding="8"
                                 FontSize="14"
                                 SelectionChanged="AccountTypeComboBox_SelectionChanged">
                            <ComboBoxItem Content="صندوق - Cash" Tag="Cash" IsSelected="True"/>
                            <ComboBoxItem Content="بنك - Bank" Tag="Bank"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <TextBlock Text="اسم الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNameTextBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Bank Information -->
                <TextBlock x:Name="BankInfoLabel" Text="معلومات البنك" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#17A2B8" Visibility="Collapsed"/>
                
                <StackPanel x:Name="BankInfoPanel" Visibility="Collapsed">
                    <TextBlock Text="اسم البنك" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="BankNameComboBox"
                             Padding="8"
                             FontSize="14"
                             Margin="0,0,0,15">
                        <ComboBoxItem Content="البنك الأهلي السعودي"/>
                        <ComboBoxItem Content="مصرف الراجحي"/>
                        <ComboBoxItem Content="بنك سامبا"/>
                        <ComboBoxItem Content="البنك السعودي للاستثمار"/>
                        <ComboBoxItem Content="البنك السعودي الفرنسي"/>
                        <ComboBoxItem Content="بنك الرياض"/>
                        <ComboBoxItem Content="البنك العربي الوطني"/>
                        <ComboBoxItem Content="بنك الجزيرة"/>
                        <ComboBoxItem Content="بنك البلاد"/>
                        <ComboBoxItem Content="بنك الإنماء"/>
                    </ComboBox>

                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="رقم الحساب البنكي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="BankAccountNumberTextBox"
                                    Padding="8"
                                    FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="رقم الآيبان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="IbanTextBox"
                                    Padding="8"
                                    FontSize="14"/>
                        </StackPanel>
                    </Grid>

                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="اسم صاحب الحساب" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="AccountHolderTextBox"
                                    Padding="8"
                                    FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="فرع البنك" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="BranchTextBox"
                                    Padding="8"
                                    FontSize="14"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>

                <!-- Financial Information -->
                <TextBlock Text="المعلومات المالية" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="العملة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CurrencyComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="ريال سعودي - SAR" Tag="SAR" IsSelected="True"/>
                            <ComboBoxItem Content="دولار أمريكي - USD" Tag="USD"/>
                            <ComboBoxItem Content="يورو - EUR" Tag="EUR"/>
                            <ComboBoxItem Content="جنيه إسترليني - GBP" Tag="GBP"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الرصيد الافتتاحي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="OpeningBalanceTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Status -->
                <CheckBox x:Name="IsActiveCheckBox"
                         Content="نشط"
                         IsChecked="True"
                         Margin="0,0,0,15"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,0,0,10"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
