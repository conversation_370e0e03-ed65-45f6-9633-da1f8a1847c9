using System;
using System.Windows;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class AddCategoryWindow : Window
    {
        private readonly CategoryViewModel _editCategory;
        private readonly bool _isEdit;

        public AddCategoryWindow(CategoryViewModel editCategory = null)
        {
            InitializeComponent();
            _editCategory = editCategory;
            _isEdit = editCategory != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل الفئة - Edit Category";
                SaveButton.Content = "تحديث";
                LoadCategoryData();
            }
            else
            {
                GenerateCategoryCode();
            }
        }

        private void GenerateCategoryCode()
        {
            // توليد كود فئة جديد
            var today = DateTime.Today;
            var categoryCode = $"CAT{today:yyyyMMdd}{new Random().Next(100, 999)}";
            CategoryCodeTextBox.Text = categoryCode;
        }

        private void LoadCategoryData()
        {
            if (_editCategory == null) return;

            CategoryCodeTextBox.Text = _editCategory.CategoryCode;
            CategoryNameTextBox.Text = _editCategory.Name;
            CategoryNameArTextBox.Text = _editCategory.NameAr;
            DescriptionTextBox.Text = _editCategory.Description;
            IsActiveCheckBox.IsChecked = _editCategory.IsActive;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                var categoryCode = CategoryCodeTextBox.Text.Trim();
                var categoryName = CategoryNameTextBox.Text.Trim();
                var categoryNameAr = CategoryNameArTextBox.Text.Trim();
                var description = DescriptionTextBox.Text.Trim();
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                // تحديث البيانات في حالة التعديل
                if (_isEdit && _editCategory != null)
                {
                    _editCategory.Name = categoryName;
                    _editCategory.NameAr = categoryNameAr;
                    _editCategory.Description = description;
                    _editCategory.IsActive = isActive;
                }

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث الفئة بنجاح!" : "تم إضافة الفئة بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من كود الفئة
            if (string.IsNullOrWhiteSpace(CategoryCodeTextBox.Text))
            {
                ShowError("يرجى إدخال كود الفئة");
                CategoryCodeTextBox.Focus();
                return false;
            }

            // التحقق من اسم الفئة الإنجليزي
            if (string.IsNullOrWhiteSpace(CategoryNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الفئة بالإنجليزية");
                CategoryNameTextBox.Focus();
                return false;
            }

            // التحقق من اسم الفئة العربي
            if (string.IsNullOrWhiteSpace(CategoryNameArTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الفئة بالعربية");
                CategoryNameArTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار كود الفئة (محاكاة)
            if (!_isEdit && IsCategoryCodeExists(CategoryCodeTextBox.Text.Trim()))
            {
                ShowError("كود الفئة موجود مسبقاً، يرجى اختيار كود آخر");
                CategoryCodeTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsCategoryCodeExists(string categoryCode)
        {
            // محاكاة التحقق من وجود كود الفئة
            var existingCodes = new[] { "CAT001", "CAT002", "CAT003", "CAT004", "CAT005" };
            return Array.Exists(existingCodes, x => x == categoryCode);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
