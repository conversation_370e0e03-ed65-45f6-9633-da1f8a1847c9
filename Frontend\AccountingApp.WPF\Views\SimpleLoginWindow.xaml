<Window x:Class="AccountingApp.WPF.Views.SimpleLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام المحاسبة المالية"
        Height="400" Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <TextBlock Text="نظام المحاسبة المالية"
                      FontSize="18"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Login Form -->
        <StackPanel Grid.Row="1" Margin="30" VerticalAlignment="Center">
            <!-- Title -->
            <TextBlock Text="تسجيل الدخول"
                      FontSize="20"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,20"/>

            <!-- Username -->
            <TextBlock Text="اسم المستخدم:" Margin="0,0,0,5"/>
            <TextBox x:Name="UsernameTextBox"
                    Text="admin"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"/>

            <!-- Password -->
            <TextBlock Text="كلمة المرور:" Margin="0,0,0,5"/>
            <PasswordBox x:Name="PasswordBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,20"/>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorTextBlock"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,15"
                      Visibility="Collapsed"/>

            <!-- Login Button -->
            <Button Content="تسجيل الدخول"
                   Background="#007ACC"
                   Foreground="White"
                   Padding="15,10"
                   FontSize="14"
                   FontWeight="SemiBold"
                   Click="LoginButton_Click"
                   Margin="0,0,0,10"/>

            <!-- Cancel Button -->
            <Button Content="إلغاء"
                   Background="#6C757D"
                   Foreground="White"
                   Padding="15,10"
                   FontSize="14"
                   FontWeight="SemiBold"
                   Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
