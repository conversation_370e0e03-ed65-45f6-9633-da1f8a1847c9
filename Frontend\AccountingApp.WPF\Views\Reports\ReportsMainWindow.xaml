<Window x:Class="AccountingApp.WPF.Views.Reports.ReportsMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المالية - Financial Reports"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#E83E8C" Padding="15">
            <Grid>
                <TextBlock Text="التقارير المالية - Financial Reports"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Quick Stats -->
                <TextBlock Text="الإحصائيات السريعة" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,0,0,15"
                          Foreground="#E83E8C"/>
                
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Sales -->
                    <Border Grid.Column="0" Background="#E8F5E8" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي المبيعات" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#388E3C"/>
                            <TextBlock x:Name="TotalSalesTextBlock" Text="245,500 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#388E3C" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Purchases -->
                    <Border Grid.Column="1" Background="#FFEBEE" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي المشتريات" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#D32F2F"/>
                            <TextBlock x:Name="TotalPurchasesTextBlock" Text="156,200 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#D32F2F" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Profit -->
                    <Border Grid.Column="2" Background="#E3F2FD" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="صافي الربح" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                            <TextBlock x:Name="NetProfitTextBlock" Text="89,300 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Inventory Value -->
                    <Border Grid.Column="3" Background="#FFF3E0" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="قيمة المخزون" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#F57C00"/>
                            <TextBlock x:Name="InventoryValueTextBlock" Text="125,500 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F57C00" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Reports Categories -->
                <TextBlock Text="فئات التقارير" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,0,0,15"
                          Foreground="#E83E8C"/>

                <!-- Sales Reports -->
                <GroupBox Header="تقارير المبيعات" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير المبيعات اليومية" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="DailySalesReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير المبيعات الشهرية" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="MonthlySalesReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير أفضل العملاء" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="TopCustomersReport_Click"/>
                        </Grid>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير الأصناف الأكثر مبيعاً" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="TopItemsReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير المبيعات حسب الفئة" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="SalesByCategoryReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير مرتجعات المبيعات" Background="#28A745" Foreground="White" Padding="15,10" Margin="5" Click="SalesReturnsReport_Click"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Purchase Reports -->
                <GroupBox Header="تقارير المشتريات" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير المشتريات اليومية" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="DailyPurchasesReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير المشتريات الشهرية" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="MonthlyPurchasesReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير أفضل الموردين" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="TopSuppliersReport_Click"/>
                        </Grid>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير المشتريات حسب الفئة" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="PurchasesByCategoryReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير أرصدة الموردين" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="SuppliersBalanceReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير مرتجعات المشتريات" Background="#DC3545" Foreground="White" Padding="15,10" Margin="5" Click="PurchaseReturnsReport_Click"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Financial Reports -->
                <GroupBox Header="التقارير المالية" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="قائمة الدخل" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="IncomeStatementReport_Click"/>
                            <Button Grid.Column="1" Content="الميزانية العمومية" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="BalanceSheetReport_Click"/>
                            <Button Grid.Column="2" Content="قائمة التدفقات النقدية" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="CashFlowReport_Click"/>
                        </Grid>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير الأرباح والخسائر" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="ProfitLossReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير الضرائب" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="TaxReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير المصروفات" Background="#6F42C1" Foreground="White" Padding="15,10" Margin="5" Click="ExpensesReport_Click"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Inventory Reports -->
                <GroupBox Header="تقارير المخزون" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير حالة المخزون" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="InventoryStatusReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير الأصناف منخفضة المخزون" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="LowStockReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير حركة المخزون" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="InventoryMovementReport_Click"/>
                        </Grid>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" Content="تقرير تقييم المخزون" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="InventoryValuationReport_Click"/>
                            <Button Grid.Column="1" Content="تقرير الأصناف الراكدة" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="SlowMovingItemsReport_Click"/>
                            <Button Grid.Column="2" Content="تقرير الجرد" Background="#17A2B8" Foreground="White" Padding="15,10" Margin="5" Click="StockTakeReport_Click"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر التقرير المطلوب" 
                          HorizontalAlignment="Right"/>
                <TextBlock Text="نظام التقارير المالية" 
                          Foreground="Green" 
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
