﻿#pragma checksum "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "838DA5C035ED3E899C0AB4676A4053FA33E4FAA3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Reports {
    
    
    /// <summary>
    /// SalesReportWindow
    /// </summary>
    public partial class SalesReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 21 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTaxTextBlock;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaidTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRemainingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/reports/salesreportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 33 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.CustomerFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            
            #line 67 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 8:
            this.InvoiceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalSalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalTaxTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TotalPaidTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalRemainingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 183 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 184 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportToExcel_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 185 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportToPDF_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 186 "..\..\..\..\..\Views\Reports\SalesReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

