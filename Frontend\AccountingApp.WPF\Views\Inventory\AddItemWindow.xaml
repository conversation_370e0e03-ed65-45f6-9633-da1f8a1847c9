<Window x:Class="AccountingApp.WPF.Views.Inventory.AddItemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة صنف جديد - Add New Item"
        Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة صنف جديد - Add New Item"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="كود الصنف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ItemNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="نوع الصنف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="ItemTypeComboBox"
                                 Padding="8"
                                 FontSize="14"
                                 SelectionChanged="ItemTypeComboBox_SelectionChanged">
                            <ComboBoxItem Content="منتج - Product" Tag="Product" IsSelected="True"/>
                            <ComboBoxItem Content="خدمة - Service" Tag="Service"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اسم الصنف (إنجليزي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ItemNameTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="اسم الصنف (عربي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ItemNameArTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الباركود" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="BarcodeTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الوحدة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="UnitComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="قطعة" IsSelected="True"/>
                            <ComboBoxItem Content="كيلو"/>
                            <ComboBoxItem Content="متر"/>
                            <ComboBoxItem Content="لتر"/>
                            <ComboBoxItem Content="حزمة"/>
                            <ComboBoxItem Content="صندوق"/>
                            <ComboBoxItem Content="ساعة"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <TextBlock Text="الفئة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CategoryComboBox"
                         Padding="8"
                         FontSize="14"
                         Margin="0,0,0,15">
                    <ComboBoxItem Content="إلكترونيات" IsSelected="True"/>
                    <ComboBoxItem Content="أثاث"/>
                    <ComboBoxItem Content="قرطاسية"/>
                    <ComboBoxItem Content="خدمات"/>
                </ComboBox>

                <!-- Pricing Information -->
                <TextBlock Text="معلومات التسعير" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="سعر الشراء" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PurchasePriceTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="سعر البيع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="SellingPriceTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Stock Information -->
                <TextBlock x:Name="StockInfoLabel" Text="معلومات المخزون" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <Grid x:Name="StockInfoPanel" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الكمية الحالية" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CurrentQuantityTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الحد الأدنى للمخزون" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="MinimumStockTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Description -->
                <TextBlock Text="الوصف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Additional Information -->
                <TextBlock Text="معلومات إضافية" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الشركة المصنعة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ManufacturerTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم الموديل" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ModelNumberTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Status -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                    <CheckBox x:Name="IsActiveCheckBox"
                             Content="نشط"
                             IsChecked="True"
                             Margin="0,0,20,0"/>
                    
                    <CheckBox x:Name="IsSaleableCheckBox"
                             Content="قابل للبيع"
                             IsChecked="True"
                             Margin="0,0,20,0"/>
                    
                    <CheckBox x:Name="IsPurchasableCheckBox"
                             Content="قابل للشراء"
                             IsChecked="True"/>
                </StackPanel>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,0,0,10"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
