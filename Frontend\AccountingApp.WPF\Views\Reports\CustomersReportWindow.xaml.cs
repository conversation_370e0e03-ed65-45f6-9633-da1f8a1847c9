using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace AccountingApp.WPF.Views.Reports
{
    public partial class CustomersReportWindow : Window
    {
        public CustomersReportWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            var customersData = new ObservableCollection<TopCustomerItem>
            {
                new TopCustomerItem
                {
                    Rank = 1,
                    CustomerName = "شركة الأعمال المتقدمة",
                    InvoiceCount = 15,
                    TotalPurchases = 125000,
                    LastPurchaseDate = DateTime.Today.AddDays(-2)
                },
                new TopCustomerItem
                {
                    Rank = 2,
                    CustomerName = "مؤسسة التجارة الحديثة",
                    InvoiceCount = 12,
                    TotalPurchases = 98500,
                    LastPurchaseDate = DateTime.Today.AddDays(-1)
                },
                new TopCustomerItem
                {
                    Rank = 3,
                    CustomerName = "شركة الخدمات المتكاملة",
                    InvoiceCount = 8,
                    TotalPurchases = 67200,
                    LastPurchaseDate = DateTime.Today.AddDays(-5)
                },
                new TopCustomerItem
                {
                    Rank = 4,
                    CustomerName = "مكتب الاستشارات القانونية",
                    InvoiceCount = 6,
                    TotalPurchases = 45800,
                    LastPurchaseDate = DateTime.Today.AddDays(-7)
                },
                new TopCustomerItem
                {
                    Rank = 5,
                    CustomerName = "شركة التطوير العقاري",
                    InvoiceCount = 4,
                    TotalPurchases = 38900,
                    LastPurchaseDate = DateTime.Today.AddDays(-10)
                }
            };

            CustomersDataGrid.ItemsSource = customersData;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة التقرير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير إلى Excel", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    public class TopCustomerItem
    {
        public int Rank { get; set; }
        public string CustomerName { get; set; } = "";
        public int InvoiceCount { get; set; }
        public decimal TotalPurchases { get; set; }
        public DateTime LastPurchaseDate { get; set; }
        
        public decimal AverageInvoice => InvoiceCount > 0 ? TotalPurchases / InvoiceCount : 0;
    }
}
