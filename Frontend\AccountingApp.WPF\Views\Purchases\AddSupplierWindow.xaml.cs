using System;
using System.Windows;

namespace AccountingApp.WPF.Views.Purchases
{
    public partial class AddSupplierWindow : Window
    {
        private readonly SupplierViewModel _editSupplier;
        private readonly bool _isEdit;

        public AddSupplierWindow(SupplierViewModel editSupplier = null)
        {
            InitializeComponent();
            _editSupplier = editSupplier;
            _isEdit = editSupplier != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل المورد - Edit Supplier";
                SaveButton.Content = "تحديث";
                LoadSupplierData();
            }
            else
            {
                GenerateSupplierNumber();
            }
        }

        private void GenerateSupplierNumber()
        {
            // توليد كود مورد جديد
            var today = DateTime.Today;
            var supplierNumber = $"SUP{today:yyyyMMdd}{new Random().Next(100, 999)}";
            SupplierNumberTextBox.Text = supplierNumber;
        }

        private void LoadSupplierData()
        {
            if (_editSupplier == null) return;

            SupplierNumberTextBox.Text = _editSupplier.SupplierNumber;
            SupplierNameTextBox.Text = _editSupplier.Name;
            TradeNameTextBox.Text = _editSupplier.TradeName;
            PhoneTextBox.Text = _editSupplier.Phone;
            EmailTextBox.Text = _editSupplier.Email;
            CityComboBox.Text = _editSupplier.City;
            IsActiveCheckBox.IsChecked = _editSupplier.IsActive;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                var supplierNumber = SupplierNumberTextBox.Text.Trim();
                var supplierName = SupplierNameTextBox.Text.Trim();
                var tradeName = TradeNameTextBox.Text.Trim();
                var phone = PhoneTextBox.Text.Trim();
                var email = EmailTextBox.Text.Trim();
                var city = CityComboBox.Text?.Trim() ?? "";
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                // تحديث البيانات في حالة التعديل
                if (_isEdit && _editSupplier != null)
                {
                    _editSupplier.Name = supplierName;
                    _editSupplier.TradeName = tradeName;
                    _editSupplier.Phone = phone;
                    _editSupplier.Email = email;
                    _editSupplier.City = city;
                    _editSupplier.IsActive = isActive;
                }

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث المورد بنجاح!" : "تم إضافة المورد بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من كود المورد
            if (string.IsNullOrWhiteSpace(SupplierNumberTextBox.Text))
            {
                ShowError("يرجى إدخال كود المورد");
                SupplierNumberTextBox.Focus();
                return false;
            }

            // التحقق من اسم المورد
            if (string.IsNullOrWhiteSpace(SupplierNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم المورد");
                SupplierNameTextBox.Focus();
                return false;
            }

            // التحقق من رقم الهاتف
            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                ShowError("يرجى إدخال رقم الهاتف");
                PhoneTextBox.Focus();
                return false;
            }

            // التحقق من المدينة
            if (CityComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار المدينة");
                CityComboBox.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                if (!IsValidEmail(EmailTextBox.Text))
                {
                    ShowError("يرجى إدخال بريد إلكتروني صحيح");
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // التحقق من حد الائتمان
            if (!decimal.TryParse(CreditLimitTextBox.Text, out var creditLimit) || creditLimit < 0)
            {
                ShowError("يرجى إدخال حد ائتمان صحيح");
                CreditLimitTextBox.Focus();
                return false;
            }

            // التحقق من مدة السداد
            if (!int.TryParse(PaymentTermsTextBox.Text, out var paymentTerms) || paymentTerms < 0)
            {
                ShowError("يرجى إدخال مدة سداد صحيحة");
                PaymentTermsTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار كود المورد (محاكاة)
            if (!_isEdit && IsSupplierNumberExists(SupplierNumberTextBox.Text.Trim()))
            {
                ShowError("كود المورد موجود مسبقاً، يرجى اختيار كود آخر");
                SupplierNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsSupplierNumberExists(string supplierNumber)
        {
            // محاكاة التحقق من وجود كود المورد
            var existingNumbers = new[] { "SUP001", "SUP002", "SUP003", "SUP004", "SUP005" };
            return Array.Exists(existingNumbers, x => x == supplierNumber);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
