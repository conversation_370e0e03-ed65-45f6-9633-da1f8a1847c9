<Window x:Class="AccountingApp.WPF.Views.Purchases.SuppliersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين - Suppliers Management"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#17A2B8" Padding="15">
            <Grid>
                <TextBlock Text="إدارة الموردين - Suppliers Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="مورد جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddSupplier_Click"/>
                
                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditSupplier_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteSupplier_Click"/>
                
                <Separator Margin="10,0"/>
                
                <Button Content="فواتير المشتريات"
                       Background="#6F42C1"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="PurchaseInvoices_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="200"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>
                
                <ComboBox x:Name="StatusFilterComboBox"
                         Width="120"
                         Margin="5"
                         SelectionChanged="StatusFilter_Changed">
                    <ComboBoxItem Content="جميع الموردين" IsSelected="True"/>
                    <ComboBoxItem Content="نشط"/>
                    <ComboBoxItem Content="غير نشط"/>
                </ComboBox>
                
                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Suppliers DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="SuppliersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="SuppliersDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود المورد" Binding="{Binding SupplierNumber}" Width="100"/>
                    <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="الاسم التجاري" Binding="{Binding TradeName}" Width="180"/>
                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                    <DataGridTextColumn Header="المدينة" Binding="{Binding City}" Width="100"/>
                    <DataGridTextColumn Header="الرصيد الحالي" Binding="{Binding CurrentBalance, StringFormat=C}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="{Binding BalanceColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="آخر معاملة" Binding="{Binding LastTransactionDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر مورد للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="SupplierCountTextBlock" Text="عدد الموردين: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalBalanceTextBlock" Text="إجمالي الأرصدة: 0.00" Foreground="Red" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
