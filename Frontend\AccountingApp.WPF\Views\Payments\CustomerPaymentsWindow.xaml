<Window x:Class="AccountingApp.WPF.Views.Payments.CustomerPaymentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="مقبوضات العملاء - Customer Receipts"
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#28A745" Padding="15">
            <Grid>
                <TextBlock Text="مقبوضات العملاء - Customer Receipts"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="1" x:Name="FromDatePicker" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="3" x:Name="ToDatePicker" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="4" Text="العميل:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="5" x:Name="CustomerFilterComboBox" Margin="0,0,20,0">
                        <ComboBoxItem Content="جميع العملاء" IsSelected="True"/>
                        <ComboBoxItem Content="شركة الأعمال المتقدمة"/>
                        <ComboBoxItem Content="مؤسسة التجارة الحديثة"/>
                        <ComboBoxItem Content="شركة الخدمات المتكاملة"/>
                    </ComboBox>
                    
                    <Button Grid.Column="6" Content="تطبيق الفلاتر" Background="#007ACC" Foreground="White" Padding="15,5" Click="ApplyFilters_Click"/>
                </Grid>
                
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <Button Content="مقبوض جديد" Background="#28A745" Foreground="White" Padding="15,8" Margin="5,0" Click="NewReceipt_Click"/>
                    <Button Content="تعديل" Background="#007ACC" Foreground="White" Padding="15,8" Margin="5,0" Click="EditReceipt_Click"/>
                    <Button Content="حذف" Background="#DC3545" Foreground="White" Padding="15,8" Margin="5,0" Click="DeleteReceipt_Click"/>
                    <Button Content="طباعة" Background="#6C757D" Foreground="White" Padding="15,8" Margin="5,0" Click="PrintReceipt_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Receipts DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="ReceiptsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="ReceiptsDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم المقبوض" Binding="{Binding ReceiptNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding ReceiptDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="200"/>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ المستلم" Binding="{Binding Amount, StringFormat=F2}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Green"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                    <DataGridTextColumn Header="رقم المرجع" Binding="{Binding ReferenceNumber}" Width="120"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Summary -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="عدد المقبوضات" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="ReceiptCountTextBlock" Text="0" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#007ACC"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المقبوضات" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalReceiptsTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="مقبوضات نقدية" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="CashReceiptsTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="مقبوضات بنكية" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="BankReceiptsTextBlock" Text="0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Blue"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4" Background="#F8F9FA" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر مقبوض للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <TextBlock Text="نظام مقبوضات العملاء" 
                          Foreground="Green" 
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
