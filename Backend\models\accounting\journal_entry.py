"""
نماذج القيود اليومية
Journal Entry Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Numeric, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class JournalEntry(Base):
    """
    نموذج القيد اليومي
    Journal Entry Model
    """
    __tablename__ = "journal_entries"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم القيد
    entry_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # تاريخ القيد
    entry_date = Column(Date, nullable=False, index=True)
    
    # وصف القيد
    description = Column(Text, nullable=False)
    
    # المرجع (رقم الفاتورة، السند، إلخ)
    reference = Column(String(50), nullable=True)
    
    # نوع القيد (manual, automatic, adjustment)
    entry_type = Column(String(20), default='manual')
    
    # إجمالي المبلغ
    total_amount = Column(Numeric(15, 2), nullable=False)
    
    # حالة القيد (draft, posted, cancelled)
    status = Column(String(20), default='draft')
    
    # تاريخ الترحيل
    posted_date = Column(DateTime(timezone=True), nullable=True)
    
    # المستخدم الذي أنشأ القيد
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # المستخدم الذي رحل القيد
    posted_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    details = relationship("JournalEntryDetail", back_populates="journal_entry", cascade="all, delete-orphan")
    creator = relationship("User", foreign_keys=[created_by])
    poster = relationship("User", foreign_keys=[posted_by])

    def __repr__(self):
        return f"<JournalEntry(id={self.id}, number='{self.entry_number}', date='{self.entry_date}')>"

    @property
    def total_debit(self):
        """إجمالي المدين"""
        return sum(detail.debit_amount for detail in self.details)

    @property
    def total_credit(self):
        """إجمالي الدائن"""
        return sum(detail.credit_amount for detail in self.details)

    @property
    def is_balanced(self):
        """التحقق من توازن القيد"""
        return abs(self.total_debit - self.total_credit) < 0.01

class JournalEntryDetail(Base):
    """
    نموذج تفاصيل القيد اليومي
    Journal Entry Detail Model
    """
    __tablename__ = "journal_entry_details"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # معرف القيد الرئيسي
    journal_entry_id = Column(Integer, ForeignKey('journal_entries.id'), nullable=False)
    
    # معرف الحساب
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    
    # وصف السطر
    description = Column(Text, nullable=True)
    
    # المبلغ المدين
    debit_amount = Column(Numeric(15, 2), default=0.00)
    
    # المبلغ الدائن
    credit_amount = Column(Numeric(15, 2), default=0.00)
    
    # ترتيب السطر
    line_order = Column(Integer, default=0)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    journal_entry = relationship("JournalEntry", back_populates="details")
    account = relationship("Account")

    def __repr__(self):
        return f"<JournalEntryDetail(id={self.id}, account_id={self.account_id}, debit={self.debit_amount}, credit={self.credit_amount})>"

    @property
    def amount(self):
        """المبلغ (مدين أو دائن)"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount

    @property
    def type(self):
        """نوع الحركة (مدين أو دائن)"""
        return 'debit' if self.debit_amount > 0 else 'credit'
