"""
تهيئة قاعدة البيانات بالبيانات الأساسية
Initialize database with basic data
"""

from sqlalchemy.orm import Session
from database.config import SessionLocal, engine
from models import *
from passlib.context import CryptContext

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return pwd_context.hash(password)

def init_account_types(db: Session):
    """تهيئة أنواع الحسابات الأساسية"""
    account_types = [
        {"name": "Assets", "name_ar": "الأصول", "code": "1", "nature": "debit", "display_order": 1},
        {"name": "Liabilities", "name_ar": "الخصوم", "code": "2", "nature": "credit", "display_order": 2},
        {"name": "Equity", "name_ar": "حقوق الملكية", "code": "3", "nature": "credit", "display_order": 3},
        {"name": "Revenue", "name_ar": "الإيرادات", "code": "4", "nature": "credit", "display_order": 4},
        {"name": "Expenses", "name_ar": "المصروفات", "code": "5", "nature": "debit", "display_order": 5},
    ]
    
    for acc_type_data in account_types:
        existing = db.query(AccountType).filter(AccountType.code == acc_type_data["code"]).first()
        if not existing:
            acc_type = AccountType(**acc_type_data)
            db.add(acc_type)
    
    db.commit()
    print("✅ تم تهيئة أنواع الحسابات")

def init_basic_accounts(db: Session):
    """تهيئة الحسابات الأساسية"""
    # الحصول على أنواع الحسابات
    assets = db.query(AccountType).filter(AccountType.code == "1").first()
    liabilities = db.query(AccountType).filter(AccountType.code == "2").first()
    equity = db.query(AccountType).filter(AccountType.code == "3").first()
    revenue = db.query(AccountType).filter(AccountType.code == "4").first()
    expenses = db.query(AccountType).filter(AccountType.code == "5").first()
    
    basic_accounts = [
        # الأصول
        {"account_number": "1001", "name": "Cash", "name_ar": "النقدية", "account_type_id": assets.id, "is_parent": False},
        {"account_number": "1002", "name": "Bank", "name_ar": "البنك", "account_type_id": assets.id, "is_parent": False},
        {"account_number": "1101", "name": "Accounts Receivable", "name_ar": "العملاء", "account_type_id": assets.id, "is_parent": False},
        {"account_number": "1201", "name": "Inventory", "name_ar": "المخزون", "account_type_id": assets.id, "is_parent": False},
        
        # الخصوم
        {"account_number": "2001", "name": "Accounts Payable", "name_ar": "الموردين", "account_type_id": liabilities.id, "is_parent": False},
        {"account_number": "2101", "name": "VAT Payable", "name_ar": "ضريبة القيمة المضافة", "account_type_id": liabilities.id, "is_parent": False},
        
        # حقوق الملكية
        {"account_number": "3001", "name": "Capital", "name_ar": "رأس المال", "account_type_id": equity.id, "is_parent": False},
        {"account_number": "3101", "name": "Retained Earnings", "name_ar": "الأرباح المحتجزة", "account_type_id": equity.id, "is_parent": False},
        
        # الإيرادات
        {"account_number": "4001", "name": "Sales Revenue", "name_ar": "إيرادات المبيعات", "account_type_id": revenue.id, "is_parent": False},
        {"account_number": "4101", "name": "Other Revenue", "name_ar": "إيرادات أخرى", "account_type_id": revenue.id, "is_parent": False},
        
        # المصروفات
        {"account_number": "5001", "name": "Cost of Goods Sold", "name_ar": "تكلفة البضاعة المباعة", "account_type_id": expenses.id, "is_parent": False},
        {"account_number": "5101", "name": "Salaries Expense", "name_ar": "مصروف الرواتب", "account_type_id": expenses.id, "is_parent": False},
        {"account_number": "5201", "name": "Rent Expense", "name_ar": "مصروف الإيجار", "account_type_id": expenses.id, "is_parent": False},
    ]
    
    for acc_data in basic_accounts:
        existing = db.query(Account).filter(Account.account_number == acc_data["account_number"]).first()
        if not existing:
            account = Account(**acc_data)
            db.add(account)
    
    db.commit()
    print("✅ تم تهيئة الحسابات الأساسية")

def init_units(db: Session):
    """تهيئة وحدات القياس الأساسية"""
    units = [
        {"name": "Piece", "name_ar": "قطعة", "symbol": "PC", "symbol_ar": "قطعة", "is_base_unit": True},
        {"name": "Kilogram", "name_ar": "كيلوجرام", "symbol": "KG", "symbol_ar": "كجم", "is_base_unit": False},
        {"name": "Meter", "name_ar": "متر", "symbol": "M", "symbol_ar": "م", "is_base_unit": False},
        {"name": "Liter", "name_ar": "لتر", "symbol": "L", "symbol_ar": "لتر", "is_base_unit": False},
        {"name": "Box", "name_ar": "صندوق", "symbol": "BOX", "symbol_ar": "صندوق", "is_base_unit": False},
        {"name": "Carton", "name_ar": "كرتون", "symbol": "CTN", "symbol_ar": "كرتون", "is_base_unit": False},
    ]
    
    for unit_data in units:
        existing = db.query(Unit).filter(Unit.symbol == unit_data["symbol"]).first()
        if not existing:
            unit = Unit(**unit_data)
            db.add(unit)
    
    db.commit()
    print("✅ تم تهيئة وحدات القياس")

def init_admin_user(db: Session):
    """تهيئة المستخدم الإداري الافتراضي"""
    existing_admin = db.query(User).filter(User.username == "admin").first()
    if not existing_admin:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hash_password("admin123"),
            full_name="مدير النظام",
            is_active=True,
            is_admin=True
        )
        db.add(admin_user)
        db.commit()
        print("✅ تم إنشاء المستخدم الإداري الافتراضي")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
    else:
        print("ℹ️  المستخدم الإداري موجود مسبقاً")

def init_default_warehouse(db: Session):
    """تهيئة المخزن الافتراضي"""
    existing = db.query(Warehouse).filter(Warehouse.warehouse_number == "WH001").first()
    if not existing:
        warehouse = Warehouse(
            warehouse_number="WH001",
            name="Main Warehouse",
            name_ar="المخزن الرئيسي",
            address="العنوان الرئيسي",
            city="الرياض",
            region="منطقة الرياض",
            is_main=True,
            is_active=True
        )
        db.add(warehouse)
        db.commit()
        print("✅ تم إنشاء المخزن الافتراضي")

def init_default_cash_account(db: Session):
    """تهيئة حساب الصندوق الافتراضي"""
    existing = db.query(CashAccount).filter(CashAccount.account_number == "CASH001").first()
    if not existing:
        cash_account = CashAccount(
            account_number="CASH001",
            name="Main Cash",
            name_ar="الصندوق الرئيسي",
            account_type="cash",
            currency="SAR",
            is_main=True,
            is_active=True
        )
        db.add(cash_account)
        db.commit()
        print("✅ تم إنشاء حساب الصندوق الافتراضي")

def initialize_database():
    """تهيئة قاعدة البيانات بالبيانات الأساسية"""
    print("🔧 بدء تهيئة قاعدة البيانات...")
    
    db = SessionLocal()
    try:
        init_account_types(db)
        init_basic_accounts(db)
        init_units(db)
        init_admin_user(db)
        init_default_warehouse(db)
        init_default_cash_account(db)
        
        print("✅ تم تهيئة قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    initialize_database()
