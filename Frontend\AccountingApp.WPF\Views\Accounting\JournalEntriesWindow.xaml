<Window x:Class="AccountingApp.WPF.Views.Accounting.JournalEntriesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="القيود اليومية - Journal Entries"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="القيود اليومية - Journal Entries"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>

                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="قيد جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="NewEntry_Click"/>

                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditEntry_Click"/>

                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteEntry_Click"/>

                <Button Content="ترحيل"
                       Background="#FFC107"
                       Foreground="Black"
                       Padding="15,8"
                       Margin="5"
                       Click="PostEntry_Click"/>

                <Separator Margin="10,0"/>

                <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="5"/>
                <DatePicker x:Name="FromDatePicker" Margin="5" SelectedDate="{x:Static sys:DateTime.Today}"/>

                <TextBlock Text="إلى تاريخ:" VerticalAlignment="Center" Margin="5"/>
                <DatePicker x:Name="ToDatePicker" Margin="5" SelectedDate="{x:Static sys:DateTime.Today}"/>

                <Button Content="بحث"
                       Background="#17A2B8"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="SearchButton_Click"/>

                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Journal Entries DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="JournalEntriesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="JournalEntriesDataGrid_SelectionChanged">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم القيد" Binding="{Binding EntryNumber}" Width="80"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding EntryDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="300"/>
                    <DataGridTextColumn Header="المرجع" Binding="{Binding Reference}" Width="100"/>
                    <DataGridTextColumn Header="المبلغ" Binding="{Binding TotalAmount, StringFormat=C}" Width="120"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المنشئ" Binding="{Binding CreatedBy}" Width="100"/>
                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=dd/MM/yyyy HH:mm}" Width="130"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock"
                          Text="جاهز - اختر قيد للعرض أو التعديل"
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="EntryCountTextBlock" Text="عدد القيود: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalAmountTextBlock" Text="إجمالي المبالغ: 0.00" Foreground="Green" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
