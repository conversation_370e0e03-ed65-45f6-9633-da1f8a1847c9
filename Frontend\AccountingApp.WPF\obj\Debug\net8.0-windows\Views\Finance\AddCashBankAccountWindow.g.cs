﻿#pragma checksum "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "24544A9773B2CE17DA0D097CFFEF302FE32C9069"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Finance {
    
    
    /// <summary>
    /// AddCashBankAccountWindow
    /// </summary>
    public partial class AddCashBankAccountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AccountTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BankInfoLabel;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BankInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BankNameComboBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BankAccountNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IbanTextBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountHolderTextBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BranchTextBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OpeningBalanceTextBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorTextBlock;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/finance/addcashbankaccountwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AccountNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AccountTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 54 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
            this.AccountTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AccountTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AccountNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BankInfoLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BankInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.BankNameComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.BankAccountNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.IbanTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.AccountHolderTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.BranchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.OpeningBalanceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.ErrorTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 211 "..\..\..\..\..\Views\Finance\AddCashBankAccountWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

