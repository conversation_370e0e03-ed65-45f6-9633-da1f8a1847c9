# تطبيق المحاسبة المالية المتكامل
## Integrated Financial Accounting Application

تطبيق محاسبة مالية احترافي متكامل للشركات المتوسطة والكبيرة مبني بـ Python (Backend) و C# WPF (Frontend)

## 🚀 التشغيل السريع

### الطريقة الأولى: التشغيل التلقائي (Windows)
```bash
# إعداد النظام لأول مرة
setup.bat

# تشغيل النظام
run_app.bat
```

### الطريقة الثانية: التشغيل اليدوي

#### 1. إعداد Backend (Python)
```bash
cd Backend
pip install -r requirements.txt
python setup_database.py
python run_server.py
```

#### 2. إعد<PERSON> Frontend (C#)
```bash
cd Frontend/AccountingApp.WPF
dotnet restore
dotnet build
dotnet run
```

## 📋 معلومات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 🌐 الروابط المهمة
- **Frontend Application**: يفتح تلقائياً
- **Backend API**: http://127.0.0.1:8000
- **API Documentation**: http://127.0.0.1:8000/docs
- **Health Check**: http://127.0.0.1:8000/health

## 🏗️ التقنيات المستخدمة

### Backend (Python)
- **FastAPI**: إطار عمل API سريع وحديث
- **SQLAlchemy**: ORM للتعامل مع قاعدة البيانات
- **SQLite/PostgreSQL**: قاعدة البيانات
- **Pydantic**: التحقق من صحة البيانات
- **JWT**: المصادقة والأمان
- **Pandas**: تحليل البيانات
- **ReportLab**: إنتاج التقارير PDF

### Frontend (C# WPF)
- **.NET 8**: إطار العمل الحديث
- **WPF**: واجهة المستخدم الرسومية
- **MVVM Pattern**: نمط التصميم
- **CommunityToolkit.Mvvm**: مكتبة MVVM
- **Material Design**: تصميم عصري
- **RTL Support**: دعم اللغة العربية

## 📁 هيكل المشروع
```
AccountingApp/
├── Backend/                    # Python FastAPI Backend
│   ├── main.py                # التطبيق الرئيسي
│   ├── models/                # نماذج قاعدة البيانات
│   ├── services/              # خدمات الأعمال
│   ├── api/                   # نقاط النهاية
│   ├── database/              # إدارة قاعدة البيانات
│   └── core/                  # الوحدات الأساسية
├── Frontend/                  # C# WPF Frontend
│   └── AccountingApp.WPF/     # مشروع WPF
│       ├── Views/             # واجهات المستخدم
│       ├── ViewModels/        # نماذج العرض
│       ├── Models/            # نماذج البيانات
│       ├── Services/          # الخدمات
│       └── Helpers/           # أدوات مساعدة
├── Shared/                    # موارد مشتركة
├── Tests/                     # الاختبارات
├── setup.bat                  # إعداد النظام
├── run_app.bat               # تشغيل النظام
└── README.md                 # هذا الملف
```

## ✨ المميزات المطبقة

### 🔐 نظام المصادقة والأمان
- تسجيل الدخول الآمن
- إدارة المستخدمين والصلاحيات
- تشفير كلمات المرور
- JWT Tokens للجلسات

### 📊 المحاسبة العامة
- دليل الحسابات متعدد المستويات
- القيود اليومية والترحيل التلقائي
- دفتر الأستاذ العام
- أنواع الحسابات (أصول، خصوم، إيرادات، مصروفات)

### 👥 إدارة العملاء والموردين
- ملفات العملاء والموردين
- الأرصدة الافتتاحية
- كشوف الحسابات
- معلومات الاتصال والضرائب

### 📦 إدارة المخازن والأصناف
- تصنيف الأصناف
- وحدات القياس
- إدارة المخازن
- تتبع حركات المخزون

### 💰 إدارة الصندوق والبنوك
- حسابات نقدية متعددة
- سندات القبض والصرف
- التحويلات بين الحسابات
- تسوية البنوك

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- قوائم منظمة وسهلة الاستخدام
- رسائل خطأ واضحة

## 🔄 المميزات المخططة

### 📋 فواتير المبيعات والمشتريات
- إنشاء وإدارة الفواتير
- مرتجعات المبيعات والمشتريات
- ربط تلقائي بالمخازن والحسابات
- طباعة الفواتير

### 📈 التقارير المالية
- قائمة الدخل
- الميزانية العمومية
- قائمة التدفقات النقدية
- تقارير مقارنة دورية
- تصدير إلى PDF وExcel

### ⚙️ إعدادات متقدمة
- إعدادات الشركة
- النسخ الاحتياطي والاستعادة
- التحديث التلقائي
- تخصيص الواجهة

## 🛠️ التطوير

### متطلبات التطوير
- **Python 3.8+** مع pip
- **.NET 8 SDK**
- **Visual Studio 2022** أو **VS Code**
- **Git** لإدارة الإصدارات

### إضافة ميزة جديدة

#### Backend (Python)
1. إنشاء النماذج في `models/`
2. إنشاء الخدمات في `services/`
3. إنشاء API في `api/`
4. تسجيل المسارات في `main.py`

#### Frontend (C#)
1. إنشاء View في `Views/`
2. إنشاء ViewModel في `ViewModels/`
3. إنشاء Models في `Models/`
4. ربط الصفحة بالقائمة الرئيسية

### تشغيل الاختبارات
```bash
# Backend Tests
cd Backend
pytest tests/

# Frontend Tests
cd Frontend/AccountingApp.WPF
dotnet test
```

## 📞 الدعم والمساعدة

### الأخطاء الشائعة
1. **خطأ في الاتصال بـ Backend**: تأكد من تشغيل Backend أولاً
2. **خطأ في قاعدة البيانات**: شغل `setup_database.py`
3. **خطأ في المكتبات**: شغل `pip install -r requirements.txt`

### الحصول على المساعدة
- راجع ملفات README في كل مجلد
- تحقق من API Documentation
- راجع سجلات الأخطاء في التطبيق

## 📄 الترخيص
هذا المشروع مطور بواسطة Augment Agent لأغراض تعليمية وتجارية.

## 🎯 الهدف من المشروع
إنشاء نظام محاسبة مالية متكامل وحديث يلبي احتياجات الشركات المتوسطة والكبيرة مع واجهة عربية سهلة الاستخدام وميزات متقدمة.
