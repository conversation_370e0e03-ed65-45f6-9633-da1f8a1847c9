<Window x:Class="AccountingApp.WPF.Views.Payments.PaymentsMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المدفوعات والمقبوضات - Payments Management"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#20C997" Padding="15">
            <Grid>
                <TextBlock Text="إدارة المدفوعات والمقبوضات - Payments Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Menu Buttons -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20">
            <StackPanel>
                <TextBlock Text="اختر نوع المعاملة المالية:" 
                          FontSize="14" 
                          FontWeight="SemiBold" 
                          Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Button Grid.Column="0" Content="مقبوضات العملاء"
                           Background="#28A745"
                           Foreground="White"
                           Padding="20,15"
                           Margin="5"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="CustomerPayments_Click"/>
                    
                    <Button Grid.Column="1" Content="مدفوعات الموردين"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="20,15"
                           Margin="5"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="SupplierPayments_Click"/>
                    
                    <Button Grid.Column="2" Content="المعاملات النقدية"
                           Background="#6F42C1"
                           Foreground="White"
                           Padding="20,15"
                           Margin="5"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="CashTransactions_Click"/>
                    
                    <Button Grid.Column="3" Content="تقارير المدفوعات"
                           Background="#E83E8C"
                           Foreground="White"
                           Padding="20,15"
                           Margin="5"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="PaymentReports_Click"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Dashboard -->
        <ScrollViewer Grid.Row="2" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Quick Stats -->
                <TextBlock Text="الإحصائيات السريعة" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,0,0,15"
                          Foreground="#20C997"/>
                
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Customer Receipts -->
                    <Border Grid.Column="0" Background="#E8F5E8" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="مقبوضات اليوم" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#388E3C"/>
                            <TextBlock x:Name="TodayReceiptsTextBlock" Text="15,500 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#388E3C" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Supplier Payments -->
                    <Border Grid.Column="1" Background="#FFEBEE" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="مدفوعات اليوم" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#D32F2F"/>
                            <TextBlock x:Name="TodayPaymentsTextBlock" Text="8,200 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#D32F2F" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Net Cash Flow -->
                    <Border Grid.Column="2" Background="#E3F2FD" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="صافي التدفق النقدي" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                            <TextBlock x:Name="NetCashFlowTextBlock" Text="+7,300 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Pending Payments -->
                    <Border Grid.Column="3" Background="#FFF3E0" Padding="15" Margin="5" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="مدفوعات معلقة" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#F57C00"/>
                            <TextBlock x:Name="PendingPaymentsTextBlock" Text="12,800 ريال" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F57C00" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Recent Transactions -->
                <TextBlock Text="آخر المعاملات المالية" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,0,0,15"
                          Foreground="#20C997"/>

                <DataGrid x:Name="RecentTransactionsDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         FontSize="12"
                         Height="200">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeText}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{Binding TypeColor}"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="العميل/المورد" Binding="{Binding PartyName}" Width="200"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=F2}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="{Binding AmountColor}"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Quick Actions -->
                <TextBlock Text="إجراءات سريعة" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,30,0,15"
                          Foreground="#20C997"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Button Grid.Column="0" Content="تسجيل مقبوض"
                           Background="#28A745"
                           Foreground="White"
                           Padding="15,10"
                           Margin="5"
                           Click="NewReceipt_Click"/>
                    
                    <Button Grid.Column="1" Content="تسجيل دفعة"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="15,10"
                           Margin="5"
                           Click="NewPayment_Click"/>
                    
                    <Button Grid.Column="2" Content="تحويل بنكي"
                           Background="#007ACC"
                           Foreground="White"
                           Padding="15,10"
                           Margin="5"
                           Click="BankTransfer_Click"/>
                    
                    <Button Grid.Column="3" Content="تسوية حسابات"
                           Background="#6F42C1"
                           Foreground="White"
                           Padding="15,10"
                           Margin="5"
                           Click="AccountReconciliation_Click"/>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر نوع المعاملة المطلوبة" 
                          HorizontalAlignment="Right"/>
                <TextBlock Text="نظام إدارة المدفوعات والمقبوضات" 
                          Foreground="Green" 
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
