using System;
using System.Threading.Tasks;
using AccountingApp.WPF.Models;

namespace AccountingApp.WPF.Services
{
    /// <summary>
    /// خدمة المصادقة
    /// Authentication Service
    /// </summary>
    public class AuthService
    {
        private readonly ApiService _apiService;
        private User? _currentUser;
        private string? _accessToken;

        public AuthService(ApiService apiService)
        {
            _apiService = apiService;
        }

        /// <summary>
        /// المستخدم الحالي
        /// Current User
        /// </summary>
        public User? CurrentUser => _currentUser;

        /// <summary>
        /// هل المستخدم مسجل دخول
        /// Is User Logged In
        /// </summary>
        public bool IsLoggedIn => _currentUser != null && !string.IsNullOrEmpty(_accessToken);

        /// <summary>
        /// تسجيل الدخول
        /// Login
        /// </summary>
        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                // نظام تسجيل دخول محلي مبسط للتجربة
                // Simple local login system for demo
                await Task.Delay(500); // محاكاة تأخير الشبكة

                // التحقق من بيانات الاعتماد المحددة مسبقاً
                if ((username == "admin" && password == "admin123") ||
                    (username == "user" && password == "user123") ||
                    (username == "مدير" && password == "123456"))
                {
                    // إنشاء مستخدم وهمي
                    _currentUser = new User
                    {
                        Id = 1,
                        Username = username,
                        Email = $"{username}@company.com",
                        FullName = username == "admin" ? "مدير النظام" :
                                  username == "مدير" ? "مدير النظام" : "مستخدم النظام",
                        IsActive = true,
                        IsAdmin = username == "admin" || username == "مدير"
                    };

                    _accessToken = "demo_token_12345";
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تسجيل الدخول: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// Logout
        /// </summary>
        public void Logout()
        {
            _currentUser = null;
            _accessToken = null;
            _apiService.ClearAccessToken();
        }

        /// <summary>
        /// التحقق من صحة الجلسة
        /// Validate Session
        /// </summary>
        public async Task<bool> ValidateSessionAsync()
        {
            if (string.IsNullOrEmpty(_accessToken))
                return false;

            try
            {
                _apiService.SetAccessToken(_accessToken);
                _currentUser = await _apiService.GetAsync<User>("auth/me");
                return _currentUser != null;
            }
            catch
            {
                Logout();
                return false;
            }
        }

        /// <summary>
        /// تحديث معلومات المستخدم الحالي
        /// Refresh Current User Info
        /// </summary>
        public async Task<bool> RefreshUserInfoAsync()
        {
            if (!IsLoggedIn)
                return false;

            try
            {
                _currentUser = await _apiService.GetAsync<User>("auth/me");
                return _currentUser != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// Check User Permission
        /// </summary>
        public bool HasPermission(string module, string action)
        {
            if (_currentUser == null)
                return false;

            // إذا كان المستخدم مدير، فله جميع الصلاحيات
            if (_currentUser.IsAdmin)
                return true;

            // TODO: تطبيق نظام الصلاحيات المفصل
            // Implement detailed permission system
            return true;
        }

        /// <summary>
        /// التحقق من كون المستخدم مدير
        /// Check if User is Admin
        /// </summary>
        public bool IsAdmin => _currentUser?.IsAdmin ?? false;

        /// <summary>
        /// الحصول على رمز الوصول
        /// Get Access Token
        /// </summary>
        public string? GetAccessToken() => _accessToken;
    }
}
