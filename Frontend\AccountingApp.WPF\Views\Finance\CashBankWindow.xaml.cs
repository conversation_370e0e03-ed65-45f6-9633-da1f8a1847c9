using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Finance
{
    public partial class CashBankWindow : Window
    {
        private ObservableCollection<CashBankAccountViewModel> _accounts;
        private CashBankAccountViewModel _selectedAccount;

        public CashBankWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _accounts = new ObservableCollection<CashBankAccountViewModel>
            {
                new CashBankAccountViewModel
                {
                    AccountNumber = "CASH001",
                    AccountName = "الصندوق الرئيسي",
                    AccountType = "Cash",
                    BankName = "",
                    BankAccountNumber = "",
                    Currency = "SAR",
                    CurrentBalance = 25000,
                    LastTransactionDate = DateTime.Today.AddDays(-1),
                    IsActive = true
                },
                new CashBankAccountViewModel
                {
                    AccountNumber = "BANK001",
                    AccountName = "البنك الأهلي - الحساب الجاري",
                    AccountType = "Bank",
                    BankName = "البنك الأهلي السعودي",
                    BankAccountNumber = "************",
                    Currency = "SAR",
                    CurrentBalance = 150000,
                    LastTransactionDate = DateTime.Today.AddDays(-2),
                    IsActive = true
                },
                new CashBankAccountViewModel
                {
                    AccountNumber = "BANK002",
                    AccountName = "بنك الراجحي - حساب التوفير",
                    AccountType = "Bank",
                    BankName = "مصرف الراجحي",
                    BankAccountNumber = "************",
                    Currency = "SAR",
                    CurrentBalance = 75000,
                    LastTransactionDate = DateTime.Today.AddDays(-5),
                    IsActive = true
                },
                new CashBankAccountViewModel
                {
                    AccountNumber = "CASH002",
                    AccountName = "صندوق الفرع الثاني",
                    AccountType = "Cash",
                    BankName = "",
                    BankAccountNumber = "",
                    Currency = "SAR",
                    CurrentBalance = 8500,
                    LastTransactionDate = DateTime.Today.AddDays(-3),
                    IsActive = true
                },
                new CashBankAccountViewModel
                {
                    AccountNumber = "BANK003",
                    AccountName = "بنك سامبا - حساب بالدولار",
                    AccountType = "Bank",
                    BankName = "بنك سامبا",
                    BankAccountNumber = "************",
                    Currency = "USD",
                    CurrentBalance = 12000,
                    LastTransactionDate = DateTime.Today.AddDays(-7),
                    IsActive = false
                }
            };

            AccountsDataGrid.ItemsSource = _accounts;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            AccountCountTextBlock.Text = $"عدد الحسابات: {_accounts.Count}";
            
            var totalCash = _accounts.Where(a => a.AccountType == "Cash" && a.Currency == "SAR")
                                   .Sum(a => a.CurrentBalance);
            TotalCashTextBlock.Text = $"إجمالي النقدية: {totalCash:C}";
            
            var totalBank = _accounts.Where(a => a.AccountType == "Bank" && a.Currency == "SAR")
                                   .Sum(a => a.CurrentBalance);
            TotalBankTextBlock.Text = $"إجمالي البنوك: {totalBank:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddAccount_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddCashBankAccountWindow();
            if (addWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة الحساب بنجاح";
            }
        }

        private void EditAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddCashBankAccountWindow(_selectedAccount);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل الحساب بنجاح";
            }
        }

        private void DeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedAccount.CurrentBalance != 0)
            {
                MessageBox.Show("لا يمكن حذف حساب يحتوي على رصيد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الحساب '{_selectedAccount.AccountName}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _accounts.Remove(_selectedAccount);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف الحساب بنجاح";
            }
        }

        private void CashMovement_Click(object sender, RoutedEventArgs e)
        {
            var movementWindow = new CashMovementWindow();
            movementWindow.ShowDialog();
        }

        private void BankTransfer_Click(object sender, RoutedEventArgs e)
        {
            var transferWindow = new BankTransferWindow();
            transferWindow.ShowDialog();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void AccountTypeFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            var selectedType = AccountTypeFilterComboBox.SelectedItem?.ToString() ?? "جميع الحسابات";
            
            if (selectedType == "جميع الحسابات")
            {
                AccountsDataGrid.ItemsSource = _accounts;
            }
            else
            {
                var accountType = selectedType == "صندوق" ? "Cash" : "Bank";
                var filteredAccounts = _accounts.Where(a => a.AccountType == accountType).ToList();
                AccountsDataGrid.ItemsSource = new ObservableCollection<CashBankAccountViewModel>(filteredAccounts);
            }
        }

        private void AccountsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedAccount = AccountsDataGrid.SelectedItem as CashBankAccountViewModel;
            
            if (_selectedAccount != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedAccount.AccountNumber} - {_selectedAccount.AccountName}";
            }
        }
    }

    public class CashBankAccountViewModel
    {
        public string AccountNumber { get; set; } = "";
        public string AccountName { get; set; } = "";
        public string AccountType { get; set; } = "";
        public string BankName { get; set; } = "";
        public string BankAccountNumber { get; set; } = "";
        public string Currency { get; set; } = "";
        public decimal CurrentBalance { get; set; }
        public DateTime LastTransactionDate { get; set; }
        public bool IsActive { get; set; }
        
        public string AccountTypeText => AccountType == "Cash" ? "صندوق" : "بنك";
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
        public string BalanceColor => CurrentBalance >= 0 ? "Green" : "Red";
    }
}
