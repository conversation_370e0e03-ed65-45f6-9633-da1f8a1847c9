﻿using System.Windows;
using AccountingApp.WPF.ViewModels;
using AccountingApp.WPF.Services;

namespace AccountingApp.WPF.Views;

/// <summary>
/// النافذة الرئيسية
/// Main Window
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;

    public MainWindow()
    {
        InitializeComponent();

        // إنشاء الخدمات
        var apiService = new ApiService();
        var authService = new AuthService(apiService);

        // إنشاء ViewModel
        _viewModel = new MainViewModel(authService);
        DataContext = _viewModel;

        // معالجة إغلاق النافذة
        Closing += MainWindow_Closing;
    }

    /// <summary>
    /// معالجة إغلاق النافذة
    /// Handle Window Closing
    /// </summary>
    private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        var result = MessageBox.Show(
            "هل تريد إغلاق النظام؟",
            "تأكيد الإغلاق",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.No)
        {
            e.Cancel = true;
        }
        else
        {
            _viewModel?.Cleanup();
        }
    }
}