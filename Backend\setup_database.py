"""
إعداد وتهيئة قاعدة البيانات
Database Setup and Initialization
"""

import sys
import os

# إضافة مسار Backend إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.config import create_tables
from database.init_data import initialize_database

def setup_database():
    """إعداد قاعدة البيانات الكاملة"""
    print("🔧 بدء إعداد قاعدة البيانات...")
    print("🔧 Starting database setup...")
    
    try:
        # إنشاء الجداول
        print("📋 إنشاء جداول قاعدة البيانات...")
        create_tables()
        print("✅ تم إنشاء الجداول بنجاح")
        
        # تهيئة البيانات الأساسية
        print("📊 تهيئة البيانات الأساسية...")
        initialize_database()
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        
        print("\n🎉 تم إعداد النظام بنجاح!")
        print("🎉 System setup completed successfully!")
        print("\n📝 معلومات تسجيل الدخول الافتراضية:")
        print("📝 Default login credentials:")
        print("   اسم المستخدم / Username: admin")
        print("   كلمة المرور / Password: admin123")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        print(f"❌ Database setup error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    setup_database()
