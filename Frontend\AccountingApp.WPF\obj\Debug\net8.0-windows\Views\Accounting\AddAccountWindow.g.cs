﻿#pragma checksum "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7641E3BED7B58669D7E677AB565F7E8251EB24A1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Accounting {
    
    
    /// <summary>
    /// AddAccountWindow
    /// </summary>
    public partial class AddAccountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNameArTextBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AccountTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ParentAccountComboBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OpeningBalanceTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsParentCheckBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/accounting/addaccountwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AccountNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AccountNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.AccountNameArTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AccountTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.ParentAccountComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.OpeningBalanceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.IsParentCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.ErrorTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 132 "..\..\..\..\..\Views\Accounting\AddAccountWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

