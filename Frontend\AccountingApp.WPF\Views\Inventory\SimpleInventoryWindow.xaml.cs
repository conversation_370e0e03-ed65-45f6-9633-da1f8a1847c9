using System;
using System.Windows;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class SimpleInventoryWindow : Window
    {
        public SimpleInventoryWindow()
        {
            InitializeComponent();
            LoadSummaryData();
        }

        private void LoadSummaryData()
        {
            // محاكاة تحميل بيانات ملخص المخزون
            TotalItemsTextBlock.Text = "25 صنف";
            LowStockItemsTextBlock.Text = "3 أصناف";
            TotalValueTextBlock.Text = "125,500 ريال";
            
            StatusTextBlock.Text = "تم تحميل البيانات بنجاح";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ManageItems_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var itemsWindow = new ItemsWindow();
                itemsWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة إدارة الأصناف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الأصناف: {ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في فتح نافذة الأصناف";
            }
        }

        private void ManageCategories_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var categoriesWindow = new CategoriesWindow();
                categoriesWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة إدارة الفئات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الفئات: {ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في فتح نافذة الفئات";
            }
        }

        private void ManageUnits_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var unitsWindow = new UnitsWindow();
                unitsWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة إدارة الوحدات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الوحدات: {ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في فتح نافذة الوحدات";
            }
        }
    }
}
