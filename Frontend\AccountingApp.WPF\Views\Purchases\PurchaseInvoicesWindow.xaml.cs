using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Purchases
{
    public partial class PurchaseInvoicesWindow : Window
    {
        private ObservableCollection<PurchaseInvoiceViewModel> _invoices;
        private PurchaseInvoiceViewModel _selectedInvoice;

        public PurchaseInvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
            SetDateFilters();
        }

        private void SetDateFilters()
        {
            // تعيين فترة افتراضية (آخر 30 يوم)
            FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Today;
        }

        private void LoadSampleData()
        {
            _invoices = new ObservableCollection<PurchaseInvoiceViewModel>
            {
                new PurchaseInvoiceViewModel
                {
                    InvoiceNumber = "PUR-2024-001",
                    InvoiceDate = DateTime.Today.AddDays(-3),
                    SupplierName = "شركة التوريدات المتقدمة",
                    SupplierInvoiceNumber = "SUP-001-2024",
                    ItemsCount = 4,
                    SubTotal = 8000,
                    TaxAmount = 1200,
                    TotalAmount = 9200,
                    PaidAmount = 9200,
                    Status = "Paid",
                    CreatedBy = "أحمد محمد"
                },
                new PurchaseInvoiceViewModel
                {
                    InvoiceNumber = "PUR-2024-002",
                    InvoiceDate = DateTime.Today.AddDays(-2),
                    SupplierName = "مؤسسة الإمدادات الصناعية",
                    SupplierInvoiceNumber = "IND-2024-045",
                    ItemsCount = 6,
                    SubTotal = 15000,
                    TaxAmount = 2250,
                    TotalAmount = 17250,
                    PaidAmount = 10000,
                    Status = "Partial",
                    CreatedBy = "فاطمة علي"
                },
                new PurchaseInvoiceViewModel
                {
                    InvoiceNumber = "PUR-2024-003",
                    InvoiceDate = DateTime.Today.AddDays(-1),
                    SupplierName = "شركة المواد الخام",
                    SupplierInvoiceNumber = "RAW-789",
                    ItemsCount = 3,
                    SubTotal = 5500,
                    TaxAmount = 825,
                    TotalAmount = 6325,
                    PaidAmount = 0,
                    Status = "Confirmed",
                    CreatedBy = "محمد أحمد"
                },
                new PurchaseInvoiceViewModel
                {
                    InvoiceNumber = "PUR-2024-004",
                    InvoiceDate = DateTime.Today,
                    SupplierName = "مكتب الخدمات التقنية",
                    SupplierInvoiceNumber = "TECH-2024-12",
                    ItemsCount = 2,
                    SubTotal = 3200,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 0,
                    Status = "Draft",
                    CreatedBy = "سارة خالد"
                },
                new PurchaseInvoiceViewModel
                {
                    InvoiceNumber = "PUR-2024-005",
                    InvoiceDate = DateTime.Today.AddDays(-7),
                    SupplierName = "شركة المعدات المكتبية",
                    SupplierInvoiceNumber = "OFF-EQ-156",
                    ItemsCount = 5,
                    SubTotal = 12000,
                    TaxAmount = 1800,
                    TotalAmount = 13800,
                    PaidAmount = 13800,
                    Status = "Paid",
                    CreatedBy = "عبدالله سعد"
                }
            };

            InvoicesDataGrid.ItemsSource = _invoices;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleInvoices = InvoicesDataGrid.ItemsSource as ObservableCollection<PurchaseInvoiceViewModel> ?? _invoices;
            
            InvoiceCountTextBlock.Text = $"عدد الفواتير: {visibleInvoices.Count}";
            
            var totalPurchases = visibleInvoices.Sum(i => i.TotalAmount);
            TotalPurchasesTextBlock.Text = $"إجمالي المشتريات: {totalPurchases:C}";
            
            var totalTax = visibleInvoices.Sum(i => i.TaxAmount);
            TotalTaxTextBlock.Text = $"إجمالي الضرائب: {totalTax:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            var newInvoiceWindow = new CreatePurchaseInvoiceWindow();
            if (newInvoiceWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إنشاء فاتورة المشتريات بنجاح";
            }
        }

        private void ViewEditInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للعرض أو التعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new CreatePurchaseInvoiceWindow(_selectedInvoice);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تحديث فاتورة المشتريات بنجاح";
            }
        }

        private void PrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show($"سيتم طباعة فاتورة المشتريات رقم: {_selectedInvoice.InvoiceNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedInvoice.Status == "Paid")
            {
                MessageBox.Show("لا يمكن حذف فاتورة مدفوعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف فاتورة المشتريات رقم '{_selectedInvoice.InvoiceNumber}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _invoices.Remove(_selectedInvoice);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف فاتورة المشتريات بنجاح";
            }
        }

        private void ManageSuppliers_Click(object sender, RoutedEventArgs e)
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            ApplyFilters();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            if (_invoices == null) return;

            var fromDate = FromDatePicker.SelectedDate ?? DateTime.MinValue;
            var toDate = ToDatePicker.SelectedDate ?? DateTime.MaxValue;
            var selectedStatus = StatusFilterComboBox.SelectedItem?.ToString() ?? "جميع الحالات";
            var searchText = SearchTextBox.Text?.ToLower() ?? "";

            var filteredInvoices = _invoices.Where(i => 
                i.InvoiceDate >= fromDate &&
                i.InvoiceDate <= toDate &&
                (selectedStatus == "جميع الحالات" || i.StatusText == selectedStatus) &&
                (string.IsNullOrWhiteSpace(searchText) || 
                 i.InvoiceNumber.ToLower().Contains(searchText) || 
                 i.SupplierName.ToLower().Contains(searchText) ||
                 i.SupplierInvoiceNumber.ToLower().Contains(searchText) ||
                 i.CreatedBy.ToLower().Contains(searchText))
            ).ToList();
            
            InvoicesDataGrid.ItemsSource = new ObservableCollection<PurchaseInvoiceViewModel>(filteredInvoices);
            UpdateSummary();
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedInvoice = InvoicesDataGrid.SelectedItem as PurchaseInvoiceViewModel;
            
            if (_selectedInvoice != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedInvoice.InvoiceNumber} - {_selectedInvoice.SupplierName}";
            }
        }
    }

    public class PurchaseInvoiceViewModel
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; } = "";
        public string SupplierInvoiceNumber { get; set; } = "";
        public int ItemsCount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        
        public string StatusText => Status switch
        {
            "Draft" => "مسودة",
            "Confirmed" => "مؤكدة",
            "Paid" => "مدفوعة",
            "Partial" => "مدفوعة جزئياً",
            "Cancelled" => "ملغاة",
            _ => "غير محدد"
        };
        
        public string StatusColor => Status switch
        {
            "Draft" => "Gray",
            "Confirmed" => "Orange",
            "Paid" => "Green",
            "Partial" => "Blue",
            "Cancelled" => "Red",
            _ => "Black"
        };
        
        public string RemainingColor => RemainingAmount > 0 ? "Red" : "Green";
    }
}
