using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Sales
{
    public partial class SalesInvoicesWindow : Window
    {
        private ObservableCollection<SalesInvoiceViewModel> _invoices;
        private SalesInvoiceViewModel _selectedInvoice;

        public SalesInvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
            SetDateFilters();
        }

        private void SetDateFilters()
        {
            // تعيين فترة افتراضية (آخر 30 يوم)
            FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Today;
        }

        private void LoadSampleData()
        {
            _invoices = new ObservableCollection<SalesInvoiceViewModel>
            {
                new SalesInvoiceViewModel
                {
                    InvoiceNumber = "INV-2024-001",
                    InvoiceDate = DateTime.Today.AddDays(-2),
                    CustomerName = "شركة التقنية المتقدمة",
                    ItemsCount = 3,
                    SubTotal = 5000,
                    TaxAmount = 750,
                    TotalAmount = 5750,
                    PaidAmount = 5750,
                    Status = "Paid",
                    CreatedBy = "أحمد محمد"
                },
                new SalesInvoiceViewModel
                {
                    InvoiceNumber = "INV-2024-002",
                    InvoiceDate = DateTime.Today.AddDays(-1),
                    CustomerName = "مؤسسة الأعمال الحديثة",
                    ItemsCount = 5,
                    SubTotal = 12000,
                    TaxAmount = 1800,
                    TotalAmount = 13800,
                    PaidAmount = 8000,
                    Status = "Partial",
                    CreatedBy = "فاطمة علي"
                },
                new SalesInvoiceViewModel
                {
                    InvoiceNumber = "INV-2024-003",
                    InvoiceDate = DateTime.Today,
                    CustomerName = "شركة الابتكار للتجارة",
                    ItemsCount = 2,
                    SubTotal = 3500,
                    TaxAmount = 525,
                    TotalAmount = 4025,
                    PaidAmount = 0,
                    Status = "Confirmed",
                    CreatedBy = "محمد أحمد"
                },
                new SalesInvoiceViewModel
                {
                    InvoiceNumber = "INV-2024-004",
                    InvoiceDate = DateTime.Today,
                    CustomerName = "مكتب الاستشارات المهنية",
                    ItemsCount = 1,
                    SubTotal = 2500,
                    TaxAmount = 375,
                    TotalAmount = 2875,
                    PaidAmount = 0,
                    Status = "Draft",
                    CreatedBy = "سارة خالد"
                },
                new SalesInvoiceViewModel
                {
                    InvoiceNumber = "INV-2024-005",
                    InvoiceDate = DateTime.Today.AddDays(-5),
                    CustomerName = "شركة التطوير العقاري",
                    ItemsCount = 4,
                    SubTotal = 8500,
                    TaxAmount = 1275,
                    TotalAmount = 9775,
                    PaidAmount = 9775,
                    Status = "Paid",
                    CreatedBy = "عبدالله سعد"
                }
            };

            InvoicesDataGrid.ItemsSource = _invoices;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleInvoices = InvoicesDataGrid.ItemsSource as ObservableCollection<SalesInvoiceViewModel> ?? _invoices;
            
            InvoiceCountTextBlock.Text = $"عدد الفواتير: {visibleInvoices.Count}";
            
            var totalSales = visibleInvoices.Sum(i => i.TotalAmount);
            TotalSalesTextBlock.Text = $"إجمالي المبيعات: {totalSales:C}";
            
            var totalTax = visibleInvoices.Sum(i => i.TaxAmount);
            TotalTaxTextBlock.Text = $"إجمالي الضرائب: {totalTax:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            var newInvoiceWindow = new CreateSalesInvoiceWindow();
            if (newInvoiceWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إنشاء الفاتورة بنجاح";
            }
        }

        private void ViewEditInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للعرض أو التعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new CreateSalesInvoiceWindow(_selectedInvoice);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تحديث الفاتورة بنجاح";
            }
        }

        private void PrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show($"سيتم طباعة الفاتورة رقم: {_selectedInvoice.InvoiceNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedInvoice.Status == "Paid")
            {
                MessageBox.Show("لا يمكن حذف فاتورة مدفوعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الفاتورة رقم '{_selectedInvoice.InvoiceNumber}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _invoices.Remove(_selectedInvoice);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف الفاتورة بنجاح";
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            ApplyFilters();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            if (_invoices == null) return;

            var fromDate = FromDatePicker.SelectedDate ?? DateTime.MinValue;
            var toDate = ToDatePicker.SelectedDate ?? DateTime.MaxValue;
            var selectedStatus = StatusFilterComboBox.SelectedItem?.ToString() ?? "جميع الحالات";
            var searchText = SearchTextBox.Text?.ToLower() ?? "";

            var filteredInvoices = _invoices.Where(i => 
                i.InvoiceDate >= fromDate &&
                i.InvoiceDate <= toDate &&
                (selectedStatus == "جميع الحالات" || i.StatusText == selectedStatus) &&
                (string.IsNullOrWhiteSpace(searchText) || 
                 i.InvoiceNumber.ToLower().Contains(searchText) || 
                 i.CustomerName.ToLower().Contains(searchText) ||
                 i.CreatedBy.ToLower().Contains(searchText))
            ).ToList();
            
            InvoicesDataGrid.ItemsSource = new ObservableCollection<SalesInvoiceViewModel>(filteredInvoices);
            UpdateSummary();
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedInvoice = InvoicesDataGrid.SelectedItem as SalesInvoiceViewModel;
            
            if (_selectedInvoice != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedInvoice.InvoiceNumber} - {_selectedInvoice.CustomerName}";
            }
        }
    }

    public class SalesInvoiceViewModel
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; } = "";
        public int ItemsCount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        
        public string StatusText => Status switch
        {
            "Draft" => "مسودة",
            "Confirmed" => "مؤكدة",
            "Paid" => "مدفوعة",
            "Partial" => "مدفوعة جزئياً",
            "Cancelled" => "ملغاة",
            _ => "غير محدد"
        };
        
        public string StatusColor => Status switch
        {
            "Draft" => "Gray",
            "Confirmed" => "Orange",
            "Paid" => "Green",
            "Partial" => "Blue",
            "Cancelled" => "Red",
            _ => "Black"
        };
        
        public string RemainingColor => RemainingAmount > 0 ? "Red" : "Green";
    }
}
