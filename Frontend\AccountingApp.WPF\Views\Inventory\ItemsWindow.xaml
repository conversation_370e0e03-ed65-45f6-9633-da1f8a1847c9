<Window x:Class="AccountingApp.WPF.Views.Inventory.ItemsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأصناف - Items Management"
        Height="700" Width="1300"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="إدارة الأصناف - Items Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="صنف جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddItem_Click"/>
                
                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditItem_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteItem_Click"/>
                
                <Separator Margin="10,0"/>
                
                <Button Content="إدارة الفئات"
                       Background="#6F42C1"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="ManageCategories_Click"/>
                
                <Button Content="إدارة الوحدات"
                       Background="#E83E8C"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="ManageUnits_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="200"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>
                
                <ComboBox x:Name="CategoryFilterComboBox"
                         Width="150"
                         Margin="5"
                         SelectionChanged="CategoryFilter_Changed">
                    <ComboBoxItem Content="جميع الفئات" IsSelected="True"/>
                </ComboBox>
                
                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Items DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="ItemsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="ItemsDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الصنف" Binding="{Binding ItemNumber}" Width="100"/>
                    <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="الاسم بالعربية" Binding="{Binding NameAr}" Width="200"/>
                    <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                    <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="120"/>
                    <DataGridTextColumn Header="الوحدة" Binding="{Binding UnitName}" Width="80"/>
                    <DataGridTextColumn Header="نوع الصنف" Binding="{Binding ItemTypeText}" Width="100"/>
                    <DataGridTextColumn Header="سعر الشراء" Binding="{Binding PurchasePrice, StringFormat=C}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Blue"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="سعر البيع" Binding="{Binding SellingPrice, StringFormat=C}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Green"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الكمية المتاحة" Binding="{Binding AvailableQuantity, StringFormat=F2}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="{Binding StockColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر صنف للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="ItemCountTextBlock" Text="عدد الأصناف: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalValueTextBlock" Text="إجمالي قيمة المخزون: 0.00" Foreground="Green" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
