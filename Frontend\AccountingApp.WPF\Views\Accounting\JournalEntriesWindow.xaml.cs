using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Accounting
{
    public partial class JournalEntriesWindow : Window
    {
        private ObservableCollection<JournalEntryViewModel> _journalEntries;
        private JournalEntryViewModel _selectedEntry;

        public JournalEntriesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _journalEntries = new ObservableCollection<JournalEntryViewModel>
            {
                new JournalEntryViewModel
                {
                    EntryNumber = "JE001",
                    EntryDate = DateTime.Today.AddDays(-5),
                    Description = "قيد افتتاحي - Opening Entry",
                    Reference = "OP001",
                    TotalAmount = 100000,
                    Status = "Posted",
                    CreatedBy = "admin",
                    CreatedAt = DateTime.Today.AddDays(-5)
                },
                new JournalEntryViewModel
                {
                    EntryNumber = "JE002",
                    EntryDate = DateTime.Today.AddDays(-3),
                    Description = "شراء معدات - Equipment Purchase",
                    Reference = "PO001",
                    TotalAmount = 25000,
                    Status = "Posted",
                    CreatedBy = "admin",
                    CreatedAt = DateTime.Today.AddDays(-3)
                },
                new JournalEntryViewModel
                {
                    EntryNumber = "JE003",
                    EntryDate = DateTime.Today.AddDays(-1),
                    Description = "مبيعات نقدية - Cash Sales",
                    Reference = "INV001",
                    TotalAmount = 15000,
                    Status = "Draft",
                    CreatedBy = "admin",
                    CreatedAt = DateTime.Today.AddDays(-1)
                },
                new JournalEntryViewModel
                {
                    EntryNumber = "JE004",
                    EntryDate = DateTime.Today,
                    Description = "دفع إيجار - Rent Payment",
                    Reference = "RENT001",
                    TotalAmount = 5000,
                    Status = "Draft",
                    CreatedBy = "admin",
                    CreatedAt = DateTime.Today
                }
            };

            JournalEntriesDataGrid.ItemsSource = _journalEntries;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            EntryCountTextBlock.Text = $"عدد القيود: {_journalEntries.Count}";
            var totalAmount = _journalEntries.Sum(e => e.TotalAmount);
            TotalAmountTextBlock.Text = $"إجمالي المبالغ: {totalAmount:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void NewEntry_Click(object sender, RoutedEventArgs e)
        {
            var newEntryWindow = new AddJournalEntryWindow();
            if (newEntryWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة القيد بنجاح";
            }
        }

        private void EditEntry_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedEntry == null)
            {
                MessageBox.Show("يرجى اختيار قيد للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedEntry.Status == "Posted")
            {
                MessageBox.Show("لا يمكن تعديل قيد مرحل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddJournalEntryWindow(_selectedEntry);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل القيد بنجاح";
            }
        }

        private void DeleteEntry_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedEntry == null)
            {
                MessageBox.Show("يرجى اختيار قيد للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedEntry.Status == "Posted")
            {
                MessageBox.Show("لا يمكن حذف قيد مرحل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف القيد '{_selectedEntry.EntryNumber}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _journalEntries.Remove(_selectedEntry);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف القيد بنجاح";
            }
        }

        private void PostEntry_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedEntry == null)
            {
                MessageBox.Show("يرجى اختيار قيد للترحيل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedEntry.Status == "Posted")
            {
                MessageBox.Show("القيد مرحل مسبقاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"هل تريد ترحيل القيد '{_selectedEntry.EntryNumber}'؟\nلن يمكن تعديله بعد الترحيل.", 
                                       "تأكيد الترحيل", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _selectedEntry.Status = "Posted";
                _selectedEntry.PostedAt = DateTime.Now;
                JournalEntriesDataGrid.Items.Refresh();
                StatusTextBlock.Text = "تم ترحيل القيد بنجاح";
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddMonths(-1);
            var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;

            var filteredEntries = _journalEntries.Where(e => e.EntryDate >= fromDate && e.EntryDate <= toDate).ToList();
            
            JournalEntriesDataGrid.ItemsSource = new ObservableCollection<JournalEntryViewModel>(filteredEntries);
            
            EntryCountTextBlock.Text = $"عدد القيود: {filteredEntries.Count}";
            var totalAmount = filteredEntries.Sum(e => e.TotalAmount);
            TotalAmountTextBlock.Text = $"إجمالي المبالغ: {totalAmount:C}";
            
            StatusTextBlock.Text = $"تم البحث من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}";
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void JournalEntriesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedEntry = JournalEntriesDataGrid.SelectedItem as JournalEntryViewModel;
            
            if (_selectedEntry != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedEntry.EntryNumber} - {_selectedEntry.Description}";
            }
        }
    }

    public class JournalEntryViewModel
    {
        public string EntryNumber { get; set; }
        public DateTime EntryDate { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? PostedAt { get; set; }
        
        public string StatusText => Status == "Posted" ? "مرحل" : "مسودة";
        public string StatusColor => Status == "Posted" ? "Green" : "Orange";
    }
}
