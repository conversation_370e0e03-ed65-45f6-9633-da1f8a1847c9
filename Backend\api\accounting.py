"""
API المحاسبة
Accounting API
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date

from database.config import get_db
from core.schemas import (
    AccountTypeResponse, AccountCreate, AccountUpdate, AccountResponse, MessageResponse
)
from core.security import require_accounting_read, require_accounting_write
from services.accounting_service import AccountingService
from models.users.user import User

router = APIRouter()

# ===== أنواع الحسابات =====

@router.get("/account-types", response_model=List[AccountTypeResponse])
async def get_account_types(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على أنواع الحسابات
    Get account types
    """
    accounting_service = AccountingService(db)
    return accounting_service.get_account_types()

# ===== الحسابات =====

@router.get("/accounts", response_model=List[AccountResponse])
async def get_accounts(
    account_type_id: Optional[int] = Query(None, description="تصفية حسب نوع الحساب"),
    parent_id: Optional[int] = Query(None, description="تصفية حسب الحساب الأب"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على الحسابات
    Get accounts with optional filtering
    """
    accounting_service = AccountingService(db)
    return accounting_service.get_accounts(account_type_id, parent_id)

@router.get("/accounts/postable", response_model=List[AccountResponse])
async def get_postable_accounts(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على الحسابات القابلة للترحيل
    Get postable accounts
    """
    accounting_service = AccountingService(db)
    return accounting_service.get_postable_accounts()

@router.get("/accounts/search/{search_term}", response_model=List[AccountResponse])
async def search_accounts(
    search_term: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    البحث في الحسابات
    Search accounts
    """
    accounting_service = AccountingService(db)
    return accounting_service.search_accounts(search_term)

@router.get("/accounts/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على حساب محدد
    Get specific account
    """
    accounting_service = AccountingService(db)
    account = accounting_service.get_account_by_id(account_id)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    return account

@router.post("/accounts", response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء حساب جديد
    Create new account
    """
    accounting_service = AccountingService(db)
    return accounting_service.create_account(account_data)

@router.put("/accounts/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: int,
    account_data: AccountUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تحديث بيانات الحساب
    Update account data
    """
    accounting_service = AccountingService(db)
    account = accounting_service.update_account(account_id, account_data)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    return account

@router.get("/accounts/{account_id}/balance")
async def get_account_balance(
    account_id: int,
    as_of_date: Optional[date] = Query(None, description="الرصيد في تاريخ معين"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على رصيد الحساب
    Get account balance
    """
    accounting_service = AccountingService(db)
    
    # التحقق من وجود الحساب
    account = accounting_service.get_account_by_id(account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    balance = accounting_service.get_account_balance(account_id, as_of_date)
    
    return {
        "account_id": account_id,
        "account_number": account.account_number,
        "account_name": account.name,
        "account_name_ar": account.name_ar,
        "balance": balance,
        "as_of_date": as_of_date or date.today(),
        "currency": "SAR"
    }

# ===== القيود اليومية =====

@router.post("/journal-entries")
async def create_journal_entry(
    entry_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء قيد يومي جديد
    Create new journal entry
    """
    accounting_service = AccountingService(db)
    
    try:
        journal_entry = accounting_service.create_journal_entry(
            entry_date=entry_data['entry_date'],
            description=entry_data['description'],
            details=entry_data['details'],
            reference=entry_data.get('reference'),
            entry_type=entry_data.get('entry_type', 'manual'),
            created_by=current_user.id
        )
        
        return {
            "id": journal_entry.id,
            "entry_number": journal_entry.entry_number,
            "entry_date": journal_entry.entry_date,
            "description": journal_entry.description,
            "total_amount": journal_entry.total_amount,
            "status": journal_entry.status,
            "message": "Journal entry created successfully",
            "message_ar": "تم إنشاء القيد اليومي بنجاح"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/journal-entries")
async def get_journal_entries(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    status: Optional[str] = Query(None, description="حالة القيد"),
    skip: int = Query(0, description="تخطي"),
    limit: int = Query(100, description="الحد الأقصى"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على القيود اليومية
    Get journal entries with filtering
    """
    accounting_service = AccountingService(db)
    entries = accounting_service.get_journal_entries(start_date, end_date, status, skip, limit)
    
    return [
        {
            "id": entry.id,
            "entry_number": entry.entry_number,
            "entry_date": entry.entry_date,
            "description": entry.description,
            "total_amount": entry.total_amount,
            "status": entry.status,
            "reference": entry.reference,
            "entry_type": entry.entry_type,
            "created_at": entry.created_at,
            "posted_date": entry.posted_date
        }
        for entry in entries
    ]

@router.post("/journal-entries/{entry_id}/post", response_model=MessageResponse)
async def post_journal_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    ترحيل القيد اليومي
    Post journal entry
    """
    accounting_service = AccountingService(db)
    success = accounting_service.post_journal_entry(entry_id, current_user.id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot post journal entry"
        )
    
    return MessageResponse(
        message="Journal entry posted successfully",
        message_ar="تم ترحيل القيد اليومي بنجاح"
    )
