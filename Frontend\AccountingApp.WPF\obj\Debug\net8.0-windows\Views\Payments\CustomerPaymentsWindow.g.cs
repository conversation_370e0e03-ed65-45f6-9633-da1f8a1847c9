﻿#pragma checksum "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3A8F319DF93765407B36E0DA397D69662B2D33BC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Payments {
    
    
    /// <summary>
    /// CustomerPaymentsWindow
    /// </summary>
    public partial class CustomerPaymentsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 51 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ReceiptsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalReceiptsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashReceiptsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BankReceiptsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/payments/customerpaymentswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 32 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.CustomerFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            
            #line 64 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 68 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewReceipt_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 69 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditReceipt_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 70 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteReceipt_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 71 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintReceipt_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ReceiptsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 87 "..\..\..\..\..\Views\Payments\CustomerPaymentsWindow.xaml"
            this.ReceiptsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ReceiptsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ReceiptCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalReceiptsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CashReceiptsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.BankReceiptsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

