"""
خادم بسيط للمحاسبة المالية
Simple Accounting Server
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sqlite3
from urllib.parse import urlparse, parse_qs

class AccountingHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)

        # إعداد CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

        if parsed_path.path == '/':
            response = {
                "message": "مرحباً بك في نظام المحاسبة المالية المتكامل",
                "message_en": "Welcome to Integrated Financial Accounting System",
                "version": "1.0.0",
                "status": "running"
            }
        elif parsed_path.path == '/health':
            response = {
                "status": "healthy",
                "message": "النظام يعمل بشكل طبيعي",
                "message_en": "System is running normally"
            }
        elif parsed_path.path == '/api/info':
            response = {
                "api_name": "نظام المحاسبة المالية",
                "api_name_en": "Financial Accounting System",
                "version": "1.0.0",
                "modules": [
                    "المحاسبة العامة - General Accounting",
                    "المبيعات والعملاء - Sales & Customers",
                    "المشتريات والموردين - Purchases & Suppliers",
                    "المخازن والأصناف - Inventory & Items",
                    "الصندوق والبنوك - Cash & Banks",
                    "التقارير المالية - Financial Reports"
                ]
            }
        elif parsed_path.path == '/api/auth/me':
            # إرجاع معلومات المستخدم الحالي (للتجربة)
            response = {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "مدير النظام",
                "phone": None,
                "is_active": True,
                "is_admin": True,
                "created_at": "2024-01-01T00:00:00",
                "last_login": None
            }
        else:
            response = {
                "error": "الصفحة غير موجودة",
                "error_en": "Page not found"
            }

        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """معالجة طلبات POST"""
        # إعداد CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

        parsed_path = urlparse(self.path)

        if parsed_path.path == '/api/auth/login':
            # قراءة بيانات الطلب
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)

            try:
                data = json.loads(post_data.decode('utf-8'))
                username = data.get('username')
                password = data.get('password')

                print(f"محاولة تسجيل دخول: {username} / {password}")  # للتشخيص

                # تحقق بسيط من بيانات تسجيل الدخول
                if username == 'admin' and password == 'admin123':
                    response = {
                        "access_token": "demo_token_12345",
                        "token_type": "bearer"
                    }
                    print("✅ تم تسجيل الدخول بنجاح")
                else:
                    response = {
                        "error": "اسم المستخدم أو كلمة المرور غير صحيحة",
                        "error_en": "Invalid username or password"
                    }
                    print("❌ فشل تسجيل الدخول")
            except Exception as e:
                print(f"خطأ في معالجة البيانات: {e}")
                response = {
                    "error": "خطأ في البيانات المرسلة",
                    "error_en": "Invalid request data"
                }
        else:
            response = {
                "error": "الصفحة غير موجودة",
                "error_en": "Page not found"
            }

        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

def run_server():
    """تشغيل الخادم"""
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, AccountingHandler)

    print("🚀 بدء تشغيل نظام المحاسبة المالية...")
    print("🚀 Starting Financial Accounting System...")
    print(f"🌐 الخادم يعمل على: http://127.0.0.1:8000")
    print(f"🌐 Server running on: http://127.0.0.1:8000")
    print("✅ النظام جاهز للعمل")
    print("✅ System ready")
    print("\n📝 معلومات تسجيل الدخول:")
    print("📝 Login credentials:")
    print("   اسم المستخدم / Username: admin")
    print("   كلمة المرور / Password: admin123")
    print("\nاضغط Ctrl+C لإيقاف الخادم")
    print("Press Ctrl+C to stop the server")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        print("🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
