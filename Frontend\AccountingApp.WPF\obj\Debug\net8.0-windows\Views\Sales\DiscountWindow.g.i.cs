﻿#pragma checksum "..\..\..\..\..\Views\Sales\DiscountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B51EFF6D7F75A959761B2BA0AC3AF122A2A7F11D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Sales {
    
    
    /// <summary>
    /// DiscountWindow
    /// </summary>
    public partial class DiscountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DiscountTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiscountValueLabel;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginalAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiscountAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FinalAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/sales/discountwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DiscountTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 34 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
            this.DiscountTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DiscountTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DiscountValueLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DiscountValueTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 46 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
            this.DiscountValueTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DiscountValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OriginalAmountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.DiscountAmountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.FinalAmountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ErrorTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            
            #line 83 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 92 "..\..\..\..\..\Views\Sales\DiscountWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

