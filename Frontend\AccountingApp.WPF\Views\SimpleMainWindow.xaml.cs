using System;
using System.Windows;
using System.Windows.Controls;
using AccountingApp.WPF.Views.Sales;
using AccountingApp.WPF.Views.Inventory;
using AccountingApp.WPF.Views.Finance;
using AccountingApp.WPF.Views.Purchases;
using AccountingApp.WPF.Views.Reports;
using AccountingApp.WPF.Views.Payments;

namespace AccountingApp.WPF.Views
{
    public partial class SimpleMainWindow : Window
    {
        public SimpleMainWindow()
        {
            InitializeComponent();
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new SimpleLoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void ShowMessage(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var moduleName = button?.Content?.ToString() ?? "الوحدة";

            // فتح الوحدات المطورة
            switch (moduleName)
            {
                case "المحاسبة العامة":
                    ShowAccountingMenu();
                    break;
                case "المبيعات والعملاء":
                    ShowCustomersWindow();
                    break;
                case "المخازن والأصناف":
                    ShowInventoryWindow();
                    break;
                case "الصندوق والبنوك":
                    ShowCashBankWindow();
                    break;
                case "فواتير المبيعات":
                    ShowSalesInvoicesWindow();
                    break;
                case "فواتير المشتريات":
                    ShowPurchaseInvoicesWindow();
                    break;
                case "المدفوعات والمقبوضات":
                    ShowPaymentsWindow();
                    break;
                case "التقارير المالية":
                    ShowReportsWindow();
                    break;
                default:
                    MessageBox.Show($"سيتم تطوير وحدة {moduleName} قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                    break;
            }
        }

        private void ModuleButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var moduleName = button?.Tag?.ToString() ?? "الوحدة";

            // فتح الوحدات المطورة
            switch (moduleName)
            {
                case "المحاسبة العامة":
                    ShowAccountingMenu();
                    break;
                case "المبيعات والعملاء":
                    ShowCustomersWindow();
                    break;
                case "المخازن والأصناف":
                    ShowInventoryWindow();
                    break;
                case "الصندوق والبنوك":
                    ShowCashBankWindow();
                    break;
                case "فواتير المبيعات":
                    ShowSalesInvoicesWindow();
                    break;
                case "فواتير المشتريات":
                    ShowPurchaseInvoicesWindow();
                    break;
                case "المدفوعات والمقبوضات":
                    ShowPaymentsWindow();
                    break;
                case "التقارير المالية":
                    ShowReportsWindow();
                    break;
                default:
                    MessageBox.Show($"سيتم تطوير وحدة {moduleName} قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                    break;
            }
        }

        private void ShowAccountingMenu()
        {
            var menu = new AccountingMenuWindow();
            menu.ShowDialog();
        }

        private void ShowCustomersWindow()
        {
            var customersWindow = new CustomersWindow();
            customersWindow.ShowDialog();
        }

        private void ShowInventoryWindow()
        {
            try
            {
                var inventoryWindow = new SimpleInventoryWindow();
                inventoryWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة المخازن: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowCashBankWindow()
        {
            try
            {
                var cashBankWindow = new SimpleCashBankWindow();
                cashBankWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الصندوق والبنوك: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowSalesInvoicesWindow()
        {
            var salesInvoicesWindow = new SalesInvoicesWindow();
            salesInvoicesWindow.ShowDialog();
        }

        private void ShowPurchaseInvoicesWindow()
        {
            var purchaseInvoicesWindow = new PurchaseInvoicesWindow();
            purchaseInvoicesWindow.ShowDialog();
        }

        private void ShowPaymentsWindow()
        {
            try
            {
                var paymentsWindow = new PaymentsMainWindow();
                paymentsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة المدفوعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowReportsWindow()
        {
            try
            {
                var reportsWindow = new ReportsMainWindow();
                reportsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
