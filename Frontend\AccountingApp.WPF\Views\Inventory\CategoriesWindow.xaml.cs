using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class CategoriesWindow : Window
    {
        private ObservableCollection<CategoryViewModel> _categories;
        private CategoryViewModel _selectedCategory;

        public CategoriesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _categories = new ObservableCollection<CategoryViewModel>
            {
                new CategoryViewModel
                {
                    CategoryCode = "CAT001",
                    Name = "Electronics",
                    NameAr = "إلكترونيات",
                    Description = "أجهزة إلكترونية وتقنية",
                    ItemCount = 25,
                    IsActive = true
                },
                new CategoryViewModel
                {
                    CategoryCode = "CAT002",
                    Name = "Furniture",
                    NameAr = "أثاث",
                    Description = "أثاث مكتبي ومنزلي",
                    ItemCount = 12,
                    IsActive = true
                },
                new CategoryViewModel
                {
                    CategoryCode = "CAT003",
                    Name = "Stationery",
                    NameAr = "قرطاسية",
                    Description = "مستلزمات مكتبية وقرطاسية",
                    ItemCount = 35,
                    IsActive = true
                },
                new CategoryViewModel
                {
                    CategoryCode = "CAT004",
                    Name = "Services",
                    NameAr = "خدمات",
                    Description = "خدمات متنوعة",
                    ItemCount = 8,
                    IsActive = true
                },
                new CategoryViewModel
                {
                    CategoryCode = "CAT005",
                    Name = "Clothing",
                    NameAr = "ملابس",
                    Description = "ملابس وأزياء",
                    ItemCount = 0,
                    IsActive = false
                }
            };

            CategoriesDataGrid.ItemsSource = _categories;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            CategoryCountTextBlock.Text = $"عدد الفئات: {_categories.Count}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddCategory_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddCategoryWindow();
            if (addWindow.ShowDialog() == true)
            {
                // إضافة الفئة الجديدة (محاكاة)
                var newCategory = new CategoryViewModel
                {
                    CategoryCode = $"CAT{DateTime.Now:yyyyMMddHHmmss}",
                    Name = "New Category",
                    NameAr = "فئة جديدة",
                    Description = "وصف الفئة الجديدة",
                    ItemCount = 0,
                    IsActive = true
                };
                
                _categories.Add(newCategory);
                UpdateSummary();
                StatusTextBlock.Text = "تم إضافة الفئة بنجاح";
            }
        }

        private void EditCategory_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCategory == null)
            {
                MessageBox.Show("يرجى اختيار فئة للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddCategoryWindow(_selectedCategory);
            if (editWindow.ShowDialog() == true)
            {
                StatusTextBlock.Text = "تم تعديل الفئة بنجاح";
                CategoriesDataGrid.Items.Refresh();
            }
        }

        private void DeleteCategory_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCategory == null)
            {
                MessageBox.Show("يرجى اختيار فئة للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedCategory.ItemCount > 0)
            {
                MessageBox.Show("لا يمكن حذف فئة تحتوي على أصناف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الفئة '{_selectedCategory.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _categories.Remove(_selectedCategory);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف الفئة بنجاح";
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                CategoriesDataGrid.ItemsSource = _categories;
            }
            else
            {
                var filteredCategories = _categories.Where(c => 
                    c.Name.ToLower().Contains(searchText) || 
                    c.NameAr.Contains(searchText) || 
                    c.CategoryCode.ToLower().Contains(searchText) ||
                    c.Description.Contains(searchText)
                ).ToList();
                
                CategoriesDataGrid.ItemsSource = new ObservableCollection<CategoryViewModel>(filteredCategories);
            }
        }

        private void CategoriesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCategory = CategoriesDataGrid.SelectedItem as CategoryViewModel;
            
            if (_selectedCategory != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedCategory.CategoryCode} - {_selectedCategory.Name}";
            }
        }
    }

    public class CategoryViewModel
    {
        public string CategoryCode { get; set; } = "";
        public string Name { get; set; } = "";
        public string NameAr { get; set; } = "";
        public string Description { get; set; } = "";
        public int ItemCount { get; set; }
        public bool IsActive { get; set; }
        
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
    }
}
