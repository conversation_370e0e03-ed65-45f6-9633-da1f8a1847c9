<Window x:Class="AccountingApp.WPF.Views.Sales.CreateSalesInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="إنشاء فاتورة مبيعات - Create Sales Invoice"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إنشاء فاتورة مبيعات - Create Sales Invoice"
                      FontSize="18"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Invoice Header Information -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="رقم الفاتورة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="InvoiceNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="0,0,10,0">
                        <TextBlock Text="تاريخ الفاتورة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="InvoiceDatePicker"
                                   Padding="8"
                                   FontSize="14"
                                   SelectedDate="{x:Static sys:DateTime.Today}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Margin="0,0,10,0">
                        <TextBlock Text="تاريخ الاستحقاق" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DueDatePicker"
                                   Padding="8"
                                   FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3">
                        <TextBlock Text="حالة الفاتورة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="StatusComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="مسودة" Tag="Draft" IsSelected="True"/>
                            <ComboBoxItem Content="مؤكدة" Tag="Confirmed"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- Second Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox x:Name="CustomerComboBox"
                                     Grid.Column="0"
                                     Padding="8"
                                     FontSize="14"
                                     SelectionChanged="CustomerComboBox_SelectionChanged">
                                <ComboBoxItem Content="شركة التقنية المتقدمة" Tag="CUST001"/>
                                <ComboBoxItem Content="مؤسسة الأعمال الحديثة" Tag="CUST002"/>
                                <ComboBoxItem Content="شركة الابتكار للتجارة" Tag="CUST003"/>
                                <ComboBoxItem Content="مكتب الاستشارات المهنية" Tag="CUST004"/>
                            </ComboBox>
                            <Button Grid.Column="1"
                                   Content="+"
                                   Background="#28A745"
                                   Foreground="White"
                                   Padding="10,8"
                                   Margin="5,0,0,0"
                                   Click="AddCustomer_Click"/>
                        </Grid>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="0,0,10,0">
                        <TextBlock Text="طريقة الدفع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="PaymentMethodComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="نقداً" IsSelected="True"/>
                            <ComboBoxItem Content="شيك"/>
                            <ComboBoxItem Content="تحويل بنكي"/>
                            <ComboBoxItem Content="بطاقة ائتمان"/>
                            <ComboBoxItem Content="آجل"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="المرجع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ReferenceTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- Invoice Items -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Items Toolbar -->
            <Border Grid.Row="0" Background="#E9ECEF" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="أصناف الفاتورة" FontSize="14" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    
                    <Button Content="إضافة صنف"
                           Background="#28A745"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="AddItem_Click"/>
                    
                    <Button Content="حذف صنف"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="RemoveItem_Click"/>
                    
                    <Button Content="تطبيق خصم"
                           Background="#FFC107"
                           Foreground="Black"
                           Padding="15,8"
                           Margin="5"
                           Click="ApplyDiscount_Click"/>
                </StackPanel>
            </Border>

            <!-- Items DataGrid -->
            <Border Grid.Row="1" Background="White" Padding="10">
                <DataGrid x:Name="ItemsDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         SelectionMode="Single"
                         GridLinesVisibility="All"
                         HeadersVisibility="Column"
                         FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="كود الصنف" Binding="{Binding ItemCode}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="اسم الصنف" Binding="{Binding ItemName}" Width="200" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat=F2}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الخصم %" Binding="{Binding DiscountPercent}" Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="قيمة الخصم" Binding="{Binding DiscountAmount, StringFormat=F2}" Width="100" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="Red"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الإجمالي" Binding="{Binding LineTotal, StringFormat=F2}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="Green"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- Add Item Panel -->
            <Border Grid.Row="2" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox x:Name="ItemCodeComboBox"
                             Grid.Column="0"
                             Padding="5"
                             Margin="2"
                             SelectionChanged="ItemCodeComboBox_SelectionChanged">
                        <ComboBoxItem Content="ITM001" Tag="ITM001"/>
                        <ComboBoxItem Content="ITM002" Tag="ITM002"/>
                        <ComboBoxItem Content="ITM003" Tag="ITM003"/>
                        <ComboBoxItem Content="ITM004" Tag="ITM004"/>
                    </ComboBox>

                    <TextBox x:Name="ItemNameTextBox"
                            Grid.Column="1"
                            Padding="5"
                            Margin="2"
                            IsReadOnly="True"
                            Background="#E9ECEF"/>

                    <TextBox x:Name="UnitTextBox"
                            Grid.Column="2"
                            Padding="5"
                            Margin="2"
                            IsReadOnly="True"
                            Background="#E9ECEF"/>

                    <TextBox x:Name="QuantityTextBox"
                            Grid.Column="3"
                            Text="1"
                            Padding="5"
                            Margin="2"/>

                    <TextBox x:Name="UnitPriceTextBox"
                            Grid.Column="4"
                            Text="0.00"
                            Padding="5"
                            Margin="2"/>

                    <TextBox x:Name="DiscountPercentTextBox"
                            Grid.Column="5"
                            Text="0"
                            Padding="5"
                            Margin="2"/>

                    <Button Grid.Column="6"
                           Content="إضافة"
                           Background="#007ACC"
                           Foreground="White"
                           Padding="10,5"
                           Margin="5,2"
                           Click="AddItemToGrid_Click"/>
                </Grid>
            </Border>
        </Grid>

        <!-- Invoice Totals -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>

                <!-- Notes -->
                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="NotesTextBox"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Padding="8"/>
                </StackPanel>

                <!-- Totals -->
                <StackPanel Grid.Column="1">
                    <Grid Margin="0,0,0,5">
                        <TextBlock Text="المجموع الفرعي:" HorizontalAlignment="Right" Margin="0,0,100,0"/>
                        <TextBox x:Name="SubTotalTextBox" 
                                Text="0.00" 
                                HorizontalAlignment="Right" 
                                Width="90" 
                                IsReadOnly="True" 
                                Background="#E9ECEF"
                                TextAlignment="Right"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,5">
                        <TextBlock Text="الخصم الإجمالي:" HorizontalAlignment="Right" Margin="0,0,100,0"/>
                        <TextBox x:Name="TotalDiscountTextBox" 
                                Text="0.00" 
                                HorizontalAlignment="Right" 
                                Width="90" 
                                IsReadOnly="True" 
                                Background="#E9ECEF"
                                TextAlignment="Right"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,5">
                        <TextBlock Text="الضريبة (15%):" HorizontalAlignment="Right" Margin="0,0,100,0"/>
                        <TextBox x:Name="TaxAmountTextBox" 
                                Text="0.00" 
                                HorizontalAlignment="Right" 
                                Width="90" 
                                IsReadOnly="True" 
                                Background="#E9ECEF"
                                TextAlignment="Right"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,5">
                        <TextBlock Text="الإجمالي النهائي:" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,0,100,0"/>
                        <TextBox x:Name="TotalAmountTextBox" 
                                Text="0.00" 
                                HorizontalAlignment="Right" 
                                Width="90" 
                                IsReadOnly="True" 
                                Background="#28A745"
                                Foreground="White"
                                FontWeight="Bold"
                                TextAlignment="Right"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="4" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="حفظ وطباعة"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveAndPrint_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
