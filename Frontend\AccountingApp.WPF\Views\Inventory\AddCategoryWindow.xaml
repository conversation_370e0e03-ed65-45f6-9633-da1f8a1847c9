<Window x:Class="AccountingApp.WPF.Views.Inventory.AddCategoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة فئة جديدة - Add New Category"
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#6F42C1" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة فئة جديدة - Add New Category"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <StackPanel Grid.Row="1" Margin="20" VerticalAlignment="Center">
            <!-- Category Code -->
            <TextBlock Text="كود الفئة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="CategoryCodeTextBox"
                    Padding="8"
                    FontSize="14"
                    IsReadOnly="True"
                    Background="#E9ECEF"
                    Margin="0,0,0,15"/>

            <!-- Category Name (English) -->
            <TextBlock Text="اسم الفئة (إنجليزي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="CategoryNameTextBox"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"/>

            <!-- Category Name (Arabic) -->
            <TextBlock Text="اسم الفئة (عربي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="CategoryNameArTextBox"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"/>

            <!-- Description -->
            <TextBlock Text="الوصف" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="DescriptionTextBox"
                    Padding="8"
                    FontSize="14"
                    Height="80"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    VerticalScrollBarVisibility="Auto"
                    Margin="0,0,0,15"/>

            <!-- Status -->
            <CheckBox x:Name="IsActiveCheckBox"
                     Content="نشط"
                     IsChecked="True"
                     Margin="0,0,0,15"/>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorTextBlock"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      Margin="0,0,0,10"
                      Visibility="Collapsed"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
