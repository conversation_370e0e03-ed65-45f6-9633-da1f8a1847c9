<Window x:Class="AccountingApp.WPF.Views.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المالية المتكامل"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="نظام المحاسبة المالية المتكامل"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>

                <Button Content="تسجيل الخروج"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="LogoutButton_Click"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="🎉 مرحباً بك في نظام المحاسبة المالية!"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="#2C3E50"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,20"/>

                <TextBlock Text="تم تسجيل الدخول بنجاح"
                          FontSize="16"
                          Foreground="#28A745"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="المحاسبة العامة"
                           Background="#007ACC"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ShowMessage"/>

                    <Button Content="المبيعات والعملاء"
                           Background="#28A745"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ShowMessage"/>

                    <Button Content="المشتريات والموردين"
                           Background="#FFC107"
                           Foreground="Black"
                           Padding="15,10"
                           Margin="10"
                           Click="ShowMessage"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <Button Content="المخازن والأصناف"
                           Background="#17A2B8"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="المخازن والأصناف"/>

                    <Button Content="الصندوق والبنوك"
                           Background="#6F42C1"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="الصندوق والبنوك"/>

                    <Button Content="فواتير المبيعات"
                           Background="#28A745"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="فواتير المبيعات"/>

                    <Button Content="فواتير المشتريات"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="فواتير المشتريات"/>

                    <Button Content="المدفوعات والمقبوضات"
                           Background="#20C997"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="المدفوعات والمقبوضات"/>

                    <Button Content="التقارير المالية"
                           Background="#E83E8C"
                           Foreground="White"
                           Padding="15,10"
                           Margin="10"
                           Click="ModuleButton_Click"
                           Tag="التقارير المالية"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock Text="جاهز - النظام يعمل بشكل طبيعي" HorizontalAlignment="Right"/>
                <TextBlock Text="متصل بالخادم" Foreground="Green" HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
