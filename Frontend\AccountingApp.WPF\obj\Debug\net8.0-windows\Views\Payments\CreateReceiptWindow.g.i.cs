﻿#pragma checksum "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E516DBDB20343CE48D6571DA2C21D49F43C1B4F8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Payments {
    
    
    /// <summary>
    /// CreateReceiptWindow
    /// </summary>
    public partial class CreateReceiptWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReceiptNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ReceiptDatePicker;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerComboBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InvoiceComboBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReferenceGrid;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReferenceLabel;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReferenceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BankTextBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryInvoiceAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryReceivedAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryRemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorTextBlock;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/payments/createreceiptwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ReceiptNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ReceiptDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.CustomerComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 72 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            this.CustomerComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CustomerComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 86 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCustomer_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.InvoiceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 104 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            this.InvoiceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InvoiceComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.InvoiceAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 138 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 146 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            this.PaymentMethodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ReferenceGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.ReferenceLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ReferenceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.BankTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SummaryInvoiceAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.SummaryReceivedAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SummaryRemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ErrorTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 237 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 246 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndPrint_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 255 "..\..\..\..\..\Views\Payments\CreateReceiptWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

