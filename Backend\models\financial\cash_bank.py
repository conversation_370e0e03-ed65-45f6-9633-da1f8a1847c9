"""
نماذج الصندوق والبنوك
Cash and Bank Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class CashAccount(Base):
    """
    نموذج حساب الصندوق/البنك
    Cash/Bank Account Model
    """
    __tablename__ = "cash_accounts"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم الحساب
    account_number = Column(String(50), unique=True, nullable=False, index=True)
    
    # اسم الحساب
    name = Column(String(100), nullable=False)
    
    # اسم الحساب بالعربية
    name_ar = Column(String(150), nullable=False)
    
    # نوع الحساب (cash, bank, credit_card)
    account_type = Column(String(20), nullable=False)
    
    # اسم البنك (للحسابات البنكية)
    bank_name = Column(String(100), nullable=True)
    
    # رقم الحساب البنكي
    bank_account_number = Column(String(50), nullable=True)
    
    # رقم الآيبان
    iban = Column(String(50), nullable=True)
    
    # رمز السويفت
    swift_code = Column(String(20), nullable=True)
    
    # العملة
    currency = Column(String(10), default='SAR')
    
    # الرصيد الافتتاحي
    opening_balance = Column(Numeric(15, 2), default=0.00)
    
    # الرصيد الحالي
    current_balance = Column(Numeric(15, 2), default=0.00)
    
    # هل الحساب رئيسي
    is_main = Column(Boolean, default=False)
    
    # هل الحساب نشط
    is_active = Column(Boolean, default=True)
    
    # ملاحظات
    notes = Column(Text, nullable=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    receipts = relationship("CashReceipt", back_populates="cash_account")
    payments = relationship("CashPayment", back_populates="cash_account")
    transfers_from = relationship("CashTransfer", foreign_keys="CashTransfer.from_account_id", back_populates="from_account")
    transfers_to = relationship("CashTransfer", foreign_keys="CashTransfer.to_account_id", back_populates="to_account")

    def __repr__(self):
        return f"<CashAccount(id={self.id}, number='{self.account_number}', name='{self.name}', type='{self.account_type}')>"

class CashReceipt(Base):
    """
    نموذج سند القبض
    Cash Receipt Model
    """
    __tablename__ = "cash_receipts"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم السند
    receipt_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # تاريخ السند
    receipt_date = Column(Date, nullable=False, index=True)
    
    # معرف حساب الصندوق/البنك
    cash_account_id = Column(Integer, ForeignKey('cash_accounts.id'), nullable=False)
    
    # المبلغ
    amount = Column(Numeric(15, 2), nullable=False)
    
    # اسم المستلم منه
    received_from = Column(String(200), nullable=False)
    
    # وصف السند
    description = Column(Text, nullable=False)
    
    # المرجع (رقم الفاتورة، إلخ)
    reference = Column(String(50), nullable=True)
    
    # نوع المرجع
    reference_type = Column(String(30), nullable=True)
    
    # معرف المرجع
    reference_id = Column(Integer, nullable=True)
    
    # حالة السند (draft, posted, cancelled)
    status = Column(String(20), default='draft')
    
    # المستخدم الذي أنشأ السند
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    cash_account = relationship("CashAccount", back_populates="receipts")
    creator = relationship("User")

    def __repr__(self):
        return f"<CashReceipt(id={self.id}, number='{self.receipt_number}', amount={self.amount})>"

class CashPayment(Base):
    """
    نموذج سند الصرف
    Cash Payment Model
    """
    __tablename__ = "cash_payments"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم السند
    payment_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # تاريخ السند
    payment_date = Column(Date, nullable=False, index=True)
    
    # معرف حساب الصندوق/البنك
    cash_account_id = Column(Integer, ForeignKey('cash_accounts.id'), nullable=False)
    
    # المبلغ
    amount = Column(Numeric(15, 2), nullable=False)
    
    # اسم المدفوع له
    paid_to = Column(String(200), nullable=False)
    
    # وصف السند
    description = Column(Text, nullable=False)
    
    # المرجع (رقم الفاتورة، إلخ)
    reference = Column(String(50), nullable=True)
    
    # نوع المرجع
    reference_type = Column(String(30), nullable=True)
    
    # معرف المرجع
    reference_id = Column(Integer, nullable=True)
    
    # حالة السند (draft, posted, cancelled)
    status = Column(String(20), default='draft')
    
    # المستخدم الذي أنشأ السند
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    cash_account = relationship("CashAccount", back_populates="payments")
    creator = relationship("User")

    def __repr__(self):
        return f"<CashPayment(id={self.id}, number='{self.payment_number}', amount={self.amount})>"

class CashTransfer(Base):
    """
    نموذج التحويل بين الحسابات
    Cash Transfer Model
    """
    __tablename__ = "cash_transfers"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم التحويل
    transfer_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # تاريخ التحويل
    transfer_date = Column(Date, nullable=False, index=True)
    
    # الحساب المحول منه
    from_account_id = Column(Integer, ForeignKey('cash_accounts.id'), nullable=False)
    
    # الحساب المحول إليه
    to_account_id = Column(Integer, ForeignKey('cash_accounts.id'), nullable=False)
    
    # المبلغ
    amount = Column(Numeric(15, 2), nullable=False)
    
    # وصف التحويل
    description = Column(Text, nullable=False)
    
    # رسوم التحويل
    transfer_fees = Column(Numeric(15, 2), default=0.00)
    
    # حالة التحويل (draft, posted, cancelled)
    status = Column(String(20), default='draft')
    
    # المستخدم الذي أنشأ التحويل
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    from_account = relationship("CashAccount", foreign_keys=[from_account_id], back_populates="transfers_from")
    to_account = relationship("CashAccount", foreign_keys=[to_account_id], back_populates="transfers_to")
    creator = relationship("User")

    def __repr__(self):
        return f"<CashTransfer(id={self.id}, number='{self.transfer_number}', amount={self.amount})>"
