# Frontend - نظام المحاسبة المالية
## Financial Accounting System Frontend

تطبيق سطح المكتب للنظام المحاسبي المتكامل مبني بـ C# WPF

### المتطلبات
- .NET 8.0 أو أحدث
- Windows 10 أو أحدث
- Visual Studio 2022 أو Visual Studio Code

### المكتبات المستخدمة
- **WPF**: واجهة المستخدم الرسومية
- **CommunityToolkit.Mvvm**: نمط MVVM
- **Newtonsoft.Json**: معالجة JSON
- **Microsoft.Extensions.Http**: HTTP Client

### هيكل المشروع

```
AccountingApp.WPF/
├── Views/                  # واجهات المستخدم (XAML)
│   ├── LoginWindow.xaml    # نافذة تسجيل الدخول
│   └── MainWindow.xaml     # النافذة الرئيسية
├── ViewModels/             # نماذج العرض (MVVM)
│   ├── BaseViewModel.cs    # ViewModel الأساسي
│   ├── LoginViewModel.cs   # ViewModel تسجيل الدخول
│   └── MainViewModel.cs    # ViewModel النافذة الرئيسية
├── Models/                 # نماذج البيانات
│   ├── User.cs            # نموذج المستخدم
│   └── Account.cs         # نموذج الحساب
├── Services/              # الخدمات
│   ├── ApiService.cs      # خدمة API الأساسية
│   └── AuthService.cs     # خدمة المصادقة
├── Controls/              # عناصر التحكم المخصصة
├── Resources/             # الموارد (صور، أيقونات، إلخ)
└── Helpers/               # أدوات مساعدة
    └── Converters.cs      # محولات الواجهة
```

### الميزات المطبقة

#### 1. نظام المصادقة
- تسجيل الدخول بالمستخدم وكلمة المرور
- واجهة تسجيل دخول عصرية
- إدارة الجلسات والرموز المميزة
- تسجيل الخروج الآمن

#### 2. الواجهة الرئيسية
- تصميم عصري ومتجاوب
- قائمة جانبية منظمة للوحدات
- شريط علوي مع معلومات المستخدم
- شريط حالة سفلي
- دعم اللغة العربية (RTL)

#### 3. نمط MVVM
- فصل واضح بين العرض والمنطق
- Data Binding متقدم
- Commands للتفاعل
- INotifyPropertyChanged للتحديثات

#### 4. خدمات API
- اتصال آمن بـ Backend
- معالجة الأخطاء
- إدارة الرموز المميزة
- طلبات HTTP متقدمة

### التشغيل

#### 1. تشغيل Backend أولاً
تأكد من تشغيل Backend API على العنوان:
```
http://127.0.0.1:8000
```

#### 2. تشغيل Frontend
```bash
cd Frontend/AccountingApp.WPF
dotnet run
```

أو من Visual Studio:
- افتح ملف `AccountingApp.WPF.csproj`
- اضغط F5 للتشغيل

### معلومات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### الوحدات المخططة

#### المحاسبة العامة
- دليل الحسابات
- القيود اليومية
- دفتر الأستاذ
- ميزان المراجعة

#### المبيعات والعملاء
- إدارة العملاء
- فواتير المبيعات
- مرتجعات المبيعات
- كشوف حسابات العملاء

#### المشتريات والموردين
- إدارة الموردين
- فواتير المشتريات
- مرتجعات المشتريات
- كشوف حسابات الموردين

#### المخازن والأصناف
- إدارة الأصناف
- إدارة المخازن
- حركات المخزون
- الجرد

#### الصندوق والبنوك
- إدارة الحسابات النقدية
- سندات القبض والصرف
- التحويلات

#### التقارير المالية
- قائمة الدخل
- الميزانية العمومية
- قائمة التدفقات النقدية
- تقارير مقارنة

### التطوير

#### إضافة صفحة جديدة
1. إنشاء XAML في مجلد `Views/`
2. إنشاء ViewModel في مجلد `ViewModels/`
3. ربط الصفحة بالقائمة الرئيسية
4. إضافة التنقل في `MainViewModel`

#### إضافة خدمة جديدة
1. إنشاء الخدمة في مجلد `Services/`
2. تسجيل الخدمة في `App.xaml.cs`
3. حقن الخدمة في ViewModels المطلوبة

### التصميم والثيمات
- دعم الثيم الفاتح والداكن (مخطط)
- تصميم Material Design
- واجهة عربية كاملة (RTL)
- ألوان متناسقة ومهنية

### الأمان
- تشفير الاتصالات
- إدارة آمنة للرموز المميزة
- التحقق من الصلاحيات
- حماية من الوصول غير المصرح

### الأداء
- تحميل البيانات بشكل غير متزامن
- واجهة متجاوبة
- إدارة ذكية للذاكرة
- تحديثات فورية للواجهة

### الدعم والصيانة
- معالجة شاملة للأخطاء
- رسائل واضحة للمستخدم
- سجلات مفصلة للأحداث
- إمكانية التحديث التلقائي
