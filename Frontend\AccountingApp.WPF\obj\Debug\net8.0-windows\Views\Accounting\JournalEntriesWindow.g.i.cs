﻿#pragma checksum "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4815F54A946911E84690EBA6C9ED2AD3922CB9FD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Accounting {
    
    
    /// <summary>
    /// JournalEntriesWindow
    /// </summary>
    public partial class JournalEntriesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 70 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid JournalEntriesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EntryCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;V1.0.0.0;component/views/accounting/journalentrieswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 32 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 44 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewEntry_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 51 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditEntry_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 58 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteEntry_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 65 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PostEntry_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            
            #line 80 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 87 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.JournalEntriesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 102 "..\..\..\..\..\Views\Accounting\JournalEntriesWindow.xaml"
            this.JournalEntriesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.JournalEntriesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.EntryCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

