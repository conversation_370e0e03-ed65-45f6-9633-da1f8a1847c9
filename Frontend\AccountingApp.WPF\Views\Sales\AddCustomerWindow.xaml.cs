using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Sales
{
    public partial class AddCustomerWindow : Window
    {
        private readonly CustomerViewModel _editCustomer;
        private readonly bool _isEdit;

        public AddCustomerWindow(CustomerViewModel editCustomer = null)
        {
            InitializeComponent();
            _editCustomer = editCustomer;
            _isEdit = editCustomer != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل العميل - Edit Customer";
                SaveButton.Content = "تحديث";
                LoadCustomerData();
            }
            else
            {
                GenerateCustomerNumber();
            }
        }

        private void GenerateCustomerNumber()
        {
            // توليد رقم عميل جديد
            var today = DateTime.Today;
            var customerNumber = $"C{today:yyyyMMdd}{new Random().Next(100, 999)}";
            CustomerNumberTextBox.Text = customerNumber;
        }

        private void LoadCustomerData()
        {
            if (_editCustomer == null) return;

            CustomerNumberTextBox.Text = _editCustomer.CustomerNumber;
            CustomerNameTextBox.Text = _editCustomer.Name;
            CustomerNameArTextBox.Text = _editCustomer.NameAr;
            PhoneTextBox.Text = _editCustomer.Phone;
            EmailTextBox.Text = _editCustomer.Email;
            CityTextBox.Text = _editCustomer.City;
            IsActiveCheckBox.IsChecked = _editCustomer.IsActive;

            // تعيين نوع العميل
            foreach (ComboBoxItem item in CustomerTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == _editCustomer.CustomerType)
                {
                    CustomerTypeComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                var customerNumber = CustomerNumberTextBox.Text.Trim();
                var customerName = CustomerNameTextBox.Text.Trim();
                var customerNameAr = CustomerNameArTextBox.Text.Trim();
                var phone = PhoneTextBox.Text.Trim();
                var email = EmailTextBox.Text.Trim();
                var city = CityTextBox.Text.Trim();
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                var selectedType = CustomerTypeComboBox.SelectedItem as ComboBoxItem;
                var customerType = selectedType?.Tag?.ToString() ?? "Individual";

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث العميل بنجاح!" : "تم إضافة العميل بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من رقم العميل
            if (string.IsNullOrWhiteSpace(CustomerNumberTextBox.Text))
            {
                ShowError("يرجى إدخال رقم العميل");
                CustomerNumberTextBox.Focus();
                return false;
            }

            // التحقق من اسم العميل الإنجليزي
            if (string.IsNullOrWhiteSpace(CustomerNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم العميل بالإنجليزية");
                CustomerNameTextBox.Focus();
                return false;
            }

            // التحقق من اسم العميل العربي
            if (string.IsNullOrWhiteSpace(CustomerNameArTextBox.Text))
            {
                ShowError("يرجى إدخال اسم العميل بالعربية");
                CustomerNameArTextBox.Focus();
                return false;
            }

            // التحقق من نوع العميل
            if (CustomerTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع العميل");
                CustomerTypeComboBox.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            {
                ShowError("يرجى إدخال بريد إلكتروني صحيح");
                EmailTextBox.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!decimal.TryParse(OpeningBalanceTextBox.Text, out _))
            {
                ShowError("يرجى إدخال رصيد افتتاحي صحيح");
                OpeningBalanceTextBox.Focus();
                return false;
            }

            // التحقق من حد الائتمان
            if (!decimal.TryParse(CreditLimitTextBox.Text, out _))
            {
                ShowError("يرجى إدخال حد ائتمان صحيح");
                CreditLimitTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار رقم العميل (محاكاة)
            if (!_isEdit && IsCustomerNumberExists(CustomerNumberTextBox.Text.Trim()))
            {
                ShowError("رقم العميل موجود مسبقاً، يرجى اختيار رقم آخر");
                CustomerNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsCustomerNumberExists(string customerNumber)
        {
            // محاكاة التحقق من وجود رقم العميل
            var existingNumbers = new[] { "C001", "C002", "C003", "C004", "C005" };
            return Array.Exists(existingNumbers, x => x == customerNumber);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
