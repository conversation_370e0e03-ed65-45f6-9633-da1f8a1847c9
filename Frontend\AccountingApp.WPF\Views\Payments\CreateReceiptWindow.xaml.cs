using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Payments
{
    public partial class CreateReceiptWindow : Window
    {
        private readonly CustomerReceiptViewModel _editReceipt;
        private readonly bool _isEdit;

        public CreateReceiptWindow(CustomerReceiptViewModel editReceipt = null)
        {
            InitializeComponent();
            _editReceipt = editReceipt;
            _isEdit = editReceipt != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل المقبوض - Edit Receipt";
                SaveButton.Content = "تحديث";
                LoadReceiptData();
            }
            else
            {
                GenerateReceiptNumber();
                ReceiptDatePicker.SelectedDate = DateTime.Today;
            }
        }

        private void GenerateReceiptNumber()
        {
            var today = DateTime.Today;
            var receiptNumber = $"REC-{today:yyyy}-{new Random().Next(1000, 9999)}";
            ReceiptNumberTextBox.Text = receiptNumber;
        }

        private void LoadReceiptData()
        {
            if (_editReceipt == null) return;

            ReceiptNumberTextBox.Text = _editReceipt.ReceiptNumber;
            ReceiptDatePicker.SelectedDate = _editReceipt.ReceiptDate;
            AmountTextBox.Text = _editReceipt.Amount.ToString("F2");
            ReferenceNumberTextBox.Text = _editReceipt.ReferenceNumber;
            NotesTextBox.Text = _editReceipt.Notes;

            // تعيين العميل
            foreach (ComboBoxItem item in CustomerComboBox.Items)
            {
                if (item.Content.ToString() == _editReceipt.CustomerName)
                {
                    CustomerComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين الفاتورة
            foreach (ComboBoxItem item in InvoiceComboBox.Items)
            {
                if (item.Content.ToString() == _editReceipt.InvoiceNumber)
                {
                    InvoiceComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين طريقة الدفع
            foreach (ComboBoxItem item in PaymentMethodComboBox.Items)
            {
                if (item.Content.ToString() == _editReceipt.PaymentMethod)
                {
                    PaymentMethodComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void CustomerComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث قائمة الفواتير حسب العميل المختار
            if (CustomerComboBox.SelectedItem is ComboBoxItem selectedCustomer)
            {
                LoadCustomerInvoices(selectedCustomer.Tag?.ToString() ?? "");
            }
        }

        private void LoadCustomerInvoices(string customerId)
        {
            InvoiceComboBox.Items.Clear();
            
            // محاكاة تحميل فواتير العميل
            switch (customerId)
            {
                case "CUS001":
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "INV-2024-001", Tag = "9775" });
                    break;
                case "CUS002":
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "INV-2024-002", Tag = "17250" });
                    break;
                case "CUS003":
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "INV-2024-003", Tag = "6325" });
                    break;
                case "CUS004":
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "INV-2024-004", Tag = "3680" });
                    break;
                case "CUS005":
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "INV-2024-005", Tag = "13800" });
                    break;
            }
        }

        private void InvoiceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InvoiceComboBox.SelectedItem is ComboBoxItem selectedInvoice)
            {
                var invoiceAmount = selectedInvoice.Tag?.ToString() ?? "0";
                InvoiceAmountTextBox.Text = $"{decimal.Parse(invoiceAmount):F2}";
                
                // تعيين المبلغ المستلم افتراضياً لكامل مبلغ الفاتورة
                if (string.IsNullOrWhiteSpace(AmountTextBox.Text))
                {
                    AmountTextBox.Text = $"{decimal.Parse(invoiceAmount):F2}";
                }
                
                UpdateSummary();
            }
        }

        private void PaymentMethodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentMethodComboBox.SelectedItem is ComboBoxItem selectedMethod)
            {
                var method = selectedMethod.Content.ToString();
                
                // إظهار/إخفاء حقول المرجع حسب طريقة الدفع
                if (method == "تحويل بنكي" || method == "شيك" || method == "حوالة")
                {
                    ReferenceGrid.Visibility = Visibility.Visible;
                    
                    // تحديث تسمية حقل المرجع
                    ReferenceLabel.Text = method switch
                    {
                        "تحويل بنكي" => "رقم التحويل",
                        "شيك" => "رقم الشيك",
                        "حوالة" => "رقم الحوالة",
                        _ => "رقم المرجع"
                    };
                }
                else
                {
                    ReferenceGrid.Visibility = Visibility.Collapsed;
                    ReferenceNumberTextBox.Text = "";
                    BankTextBox.Text = "";
                }
            }
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            if (decimal.TryParse(InvoiceAmountTextBox.Text, out var invoiceAmount))
            {
                SummaryInvoiceAmountTextBlock.Text = $"{invoiceAmount:F2} ريال";
            }

            if (decimal.TryParse(AmountTextBox.Text, out var receivedAmount))
            {
                SummaryReceivedAmountTextBlock.Text = $"{receivedAmount:F2} ريال";
                
                var remainingAmount = invoiceAmount - receivedAmount;
                SummaryRemainingAmountTextBlock.Text = $"{remainingAmount:F2} ريال";
                SummaryRemainingAmountTextBlock.Foreground = remainingAmount > 0 ? 
                    System.Windows.Media.Brushes.Red : System.Windows.Media.Brushes.Green;
            }
        }

        private void AddCustomer_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة عميل جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateReceipt())
                return;

            try
            {
                // محاكاة حفظ المقبوض
                var message = _isEdit ? "تم تحديث المقبوض بنجاح!" : "تم إنشاء المقبوض بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ المقبوض: {ex.Message}");
            }
        }

        private void SaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            SaveButton_Click(sender, e);
            if (DialogResult == true)
            {
                MessageBox.Show("سيتم طباعة المقبوض", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool ValidateReceipt()
        {
            // التحقق من رقم المقبوض
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                ShowError("يرجى إدخال رقم المقبوض");
                return false;
            }

            // التحقق من العميل
            if (CustomerComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار العميل");
                CustomerComboBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            {
                ShowError("يرجى إدخال مبلغ صحيح");
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من طريقة الدفع
            if (PaymentMethodComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار طريقة الدفع");
                PaymentMethodComboBox.Focus();
                return false;
            }

            // التحقق من رقم المرجع للطرق التي تتطلبه
            if (ReferenceGrid.Visibility == Visibility.Visible && 
                string.IsNullOrWhiteSpace(ReferenceNumberTextBox.Text))
            {
                ShowError("يرجى إدخال رقم المرجع");
                ReferenceNumberTextBox.Focus();
                return false;
            }

            // التحقق من عدم تجاوز مبلغ الفاتورة
            if (decimal.TryParse(InvoiceAmountTextBox.Text, out var invoiceAmount) && amount > invoiceAmount)
            {
                ShowError("لا يمكن أن يتجاوز المبلغ المستلم مبلغ الفاتورة");
                AmountTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
