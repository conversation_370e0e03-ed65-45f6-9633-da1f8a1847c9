using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class AddItemWindow : Window
    {
        private readonly ItemViewModel _editItem;
        private readonly bool _isEdit;

        public AddItemWindow(ItemViewModel editItem = null)
        {
            InitializeComponent();
            _editItem = editItem;
            _isEdit = editItem != null;
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل الصنف - Edit Item";
                SaveButton.Content = "تحديث";
                LoadItemData();
            }
            else
            {
                GenerateItemNumber();
            }
        }

        private void GenerateItemNumber()
        {
            // توليد كود صنف جديد
            var today = DateTime.Today;
            var itemNumber = $"ITM{today:yyyyMMdd}{new Random().Next(100, 999)}";
            ItemNumberTextBox.Text = itemNumber;
        }

        private void LoadItemData()
        {
            if (_editItem == null) return;

            ItemNumberTextBox.Text = _editItem.ItemNumber;
            ItemNameTextBox.Text = _editItem.Name;
            ItemNameArTextBox.Text = _editItem.NameAr;
            BarcodeTextBox.Text = _editItem.Barcode;
            PurchasePriceTextBox.Text = _editItem.PurchasePrice.ToString("F2");
            SellingPriceTextBox.Text = _editItem.SellingPrice.ToString("F2");
            CurrentQuantityTextBox.Text = _editItem.AvailableQuantity.ToString("F2");
            MinimumStockTextBox.Text = _editItem.MinimumStock.ToString("F2");
            IsActiveCheckBox.IsChecked = _editItem.IsActive;

            // تعيين نوع الصنف
            foreach (ComboBoxItem item in ItemTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == _editItem.ItemType)
                {
                    ItemTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين الوحدة
            UnitComboBox.Text = _editItem.UnitName;
            
            // تعيين الفئة
            CategoryComboBox.Text = _editItem.CategoryName;
        }

        private void ItemTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ItemTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var itemType = selectedItem.Tag?.ToString();
                
                // إخفاء/إظهار معلومات المخزون حسب نوع الصنف
                if (itemType == "Service")
                {
                    StockInfoLabel.Visibility = Visibility.Collapsed;
                    StockInfoPanel.Visibility = Visibility.Collapsed;
                    CurrentQuantityTextBox.Text = "0";
                    MinimumStockTextBox.Text = "0";
                    PurchasePriceTextBox.Text = "0";
                }
                else
                {
                    StockInfoLabel.Visibility = Visibility.Visible;
                    StockInfoPanel.Visibility = Visibility.Visible;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                var itemNumber = ItemNumberTextBox.Text.Trim();
                var itemName = ItemNameTextBox.Text.Trim();
                var itemNameAr = ItemNameArTextBox.Text.Trim();
                var barcode = BarcodeTextBox.Text.Trim();
                var purchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
                var sellingPrice = decimal.Parse(SellingPriceTextBox.Text);
                var currentQuantity = decimal.Parse(CurrentQuantityTextBox.Text);
                var minimumStock = decimal.Parse(MinimumStockTextBox.Text);
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                var selectedType = ItemTypeComboBox.SelectedItem as ComboBoxItem;
                var itemType = selectedType?.Tag?.ToString() ?? "Product";

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث الصنف بنجاح!" : "تم إضافة الصنف بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من كود الصنف
            if (string.IsNullOrWhiteSpace(ItemNumberTextBox.Text))
            {
                ShowError("يرجى إدخال كود الصنف");
                ItemNumberTextBox.Focus();
                return false;
            }

            // التحقق من اسم الصنف الإنجليزي
            if (string.IsNullOrWhiteSpace(ItemNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الصنف بالإنجليزية");
                ItemNameTextBox.Focus();
                return false;
            }

            // التحقق من اسم الصنف العربي
            if (string.IsNullOrWhiteSpace(ItemNameArTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الصنف بالعربية");
                ItemNameArTextBox.Focus();
                return false;
            }

            // التحقق من نوع الصنف
            if (ItemTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع الصنف");
                ItemTypeComboBox.Focus();
                return false;
            }

            // التحقق من الوحدة
            if (UnitComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الوحدة");
                UnitComboBox.Focus();
                return false;
            }

            // التحقق من الفئة
            if (CategoryComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الفئة");
                CategoryComboBox.Focus();
                return false;
            }

            // التحقق من سعر الشراء
            if (!decimal.TryParse(PurchasePriceTextBox.Text, out var purchasePrice) || purchasePrice < 0)
            {
                ShowError("يرجى إدخال سعر شراء صحيح");
                PurchasePriceTextBox.Focus();
                return false;
            }

            // التحقق من سعر البيع
            if (!decimal.TryParse(SellingPriceTextBox.Text, out var sellingPrice) || sellingPrice <= 0)
            {
                ShowError("يرجى إدخال سعر بيع صحيح");
                SellingPriceTextBox.Focus();
                return false;
            }

            // التحقق من الكمية الحالية
            if (!decimal.TryParse(CurrentQuantityTextBox.Text, out var currentQuantity) || currentQuantity < 0)
            {
                ShowError("يرجى إدخال كمية حالية صحيحة");
                CurrentQuantityTextBox.Focus();
                return false;
            }

            // التحقق من الحد الأدنى للمخزون
            if (!decimal.TryParse(MinimumStockTextBox.Text, out var minimumStock) || minimumStock < 0)
            {
                ShowError("يرجى إدخال حد أدنى للمخزون صحيح");
                MinimumStockTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار كود الصنف (محاكاة)
            if (!_isEdit && IsItemNumberExists(ItemNumberTextBox.Text.Trim()))
            {
                ShowError("كود الصنف موجود مسبقاً، يرجى اختيار كود آخر");
                ItemNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsItemNumberExists(string itemNumber)
        {
            // محاكاة التحقق من وجود كود الصنف
            var existingNumbers = new[] { "ITM001", "ITM002", "ITM003", "ITM004", "SRV001" };
            return Array.Exists(existingNumbers, x => x == itemNumber);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
