<Window x:Class="AccountingApp.WPF.Views.AccountingMenuWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="المحاسبة العامة - General Accounting"
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="المحاسبة العامة - General Accounting"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Menu Items -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <TextBlock Text="اختر الوحدة المطلوبة:"
                          FontSize="16"
                          FontWeight="SemiBold"
                          Margin="0,0,0,20"
                          HorizontalAlignment="Center"/>

                <!-- Chart of Accounts -->
                <Border Background="White" 
                       BorderBrush="#DEE2E6" 
                       BorderThickness="1" 
                       CornerRadius="5" 
                       Margin="0,0,0,15"
                       Cursor="Hand"
                       MouseLeftButtonUp="ChartOfAccounts_Click">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" 
                               Background="#007ACC" 
                               Width="60" 
                               Height="60" 
                               CornerRadius="30"
                               Margin="0,0,15,0">
                            <TextBlock Text="📊" 
                                     FontSize="24" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     Foreground="White"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="دليل الحسابات" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Margin="0,0,0,5"/>
                            <TextBlock Text="Chart of Accounts" 
                                     FontSize="12" 
                                     Foreground="#6C757D"/>
                            <TextBlock Text="إدارة وعرض جميع الحسابات المالية" 
                                     FontSize="11" 
                                     Foreground="#6C757D" 
                                     TextWrapping="Wrap"/>
                        </StackPanel>

                        <TextBlock Grid.Column="2" 
                                  Text="←" 
                                  FontSize="20" 
                                  VerticalAlignment="Center" 
                                  Foreground="#007ACC"/>
                    </Grid>
                </Border>

                <!-- Journal Entries -->
                <Border Background="White" 
                       BorderBrush="#DEE2E6" 
                       BorderThickness="1" 
                       CornerRadius="5" 
                       Margin="0,0,0,15"
                       Cursor="Hand"
                       MouseLeftButtonUp="JournalEntries_Click">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" 
                               Background="#28A745" 
                               Width="60" 
                               Height="60" 
                               CornerRadius="30"
                               Margin="0,0,15,0">
                            <TextBlock Text="📝" 
                                     FontSize="24" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     Foreground="White"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="القيود اليومية" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Margin="0,0,0,5"/>
                            <TextBlock Text="Journal Entries" 
                                     FontSize="12" 
                                     Foreground="#6C757D"/>
                            <TextBlock Text="إدخال وإدارة القيود المحاسبية اليومية" 
                                     FontSize="11" 
                                     Foreground="#6C757D" 
                                     TextWrapping="Wrap"/>
                        </StackPanel>

                        <TextBlock Grid.Column="2" 
                                  Text="←" 
                                  FontSize="20" 
                                  VerticalAlignment="Center" 
                                  Foreground="#28A745"/>
                    </Grid>
                </Border>

                <!-- Trial Balance -->
                <Border Background="White" 
                       BorderBrush="#DEE2E6" 
                       BorderThickness="1" 
                       CornerRadius="5" 
                       Margin="0,0,0,15"
                       Cursor="Hand"
                       MouseLeftButtonUp="TrialBalance_Click">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" 
                               Background="#17A2B8" 
                               Width="60" 
                               Height="60" 
                               CornerRadius="30"
                               Margin="0,0,15,0">
                            <TextBlock Text="⚖️" 
                                     FontSize="24" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     Foreground="White"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="ميزان المراجعة" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Margin="0,0,0,5"/>
                            <TextBlock Text="Trial Balance" 
                                     FontSize="12" 
                                     Foreground="#6C757D"/>
                            <TextBlock Text="عرض أرصدة جميع الحسابات للتأكد من التوازن" 
                                     FontSize="11" 
                                     Foreground="#6C757D" 
                                     TextWrapping="Wrap"/>
                        </StackPanel>

                        <TextBlock Grid.Column="2" 
                                  Text="←" 
                                  FontSize="20" 
                                  VerticalAlignment="Center" 
                                  Foreground="#17A2B8"/>
                    </Grid>
                </Border>

                <!-- General Ledger -->
                <Border Background="White" 
                       BorderBrush="#DEE2E6" 
                       BorderThickness="1" 
                       CornerRadius="5" 
                       Margin="0,0,0,15"
                       Cursor="Hand"
                       MouseLeftButtonUp="GeneralLedger_Click">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" 
                               Background="#6F42C1" 
                               Width="60" 
                               Height="60" 
                               CornerRadius="30"
                               Margin="0,0,15,0">
                            <TextBlock Text="📚" 
                                     FontSize="24" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     Foreground="White"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="دفتر الأستاذ العام" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Margin="0,0,0,5"/>
                            <TextBlock Text="General Ledger" 
                                     FontSize="12" 
                                     Foreground="#6C757D"/>
                            <TextBlock Text="عرض تفاصيل حركة الحسابات" 
                                     FontSize="11" 
                                     Foreground="#6C757D" 
                                     TextWrapping="Wrap"/>
                        </StackPanel>

                        <TextBlock Grid.Column="2" 
                                  Text="←" 
                                  FontSize="20" 
                                  VerticalAlignment="Center" 
                                  Foreground="#6F42C1"/>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#E9ECEF" Padding="10">
            <TextBlock Text="اختر الوحدة المطلوبة من القائمة أعلاه" 
                      HorizontalAlignment="Center" 
                      Foreground="#6C757D"/>
        </Border>
    </Grid>
</Window>
