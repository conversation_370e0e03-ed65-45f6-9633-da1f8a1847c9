<Window x:Class="AccountingApp.WPF.Views.Purchases.AddSupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مورد جديد - Add New Supplier"
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#17A2B8" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة مورد جديد - Add New Supplier"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="كود المورد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="SupplierNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="نوع المورد" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="SupplierTypeComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="شركة" IsSelected="True"/>
                            <ComboBoxItem Content="مؤسسة"/>
                            <ComboBoxItem Content="فرد"/>
                            <ComboBoxItem Content="مكتب"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اسم المورد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="SupplierNameTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الاسم التجاري" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TradeNameTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Contact Information -->
                <TextBlock Text="معلومات الاتصال" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الهاتف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم الجوال" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="MobileTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="EmailTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الموقع الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="WebsiteTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Address Information -->
                <TextBlock Text="معلومات العنوان" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المدينة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CityComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="الرياض" IsSelected="True"/>
                            <ComboBoxItem Content="جدة"/>
                            <ComboBoxItem Content="الدمام"/>
                            <ComboBoxItem Content="مكة المكرمة"/>
                            <ComboBoxItem Content="المدينة المنورة"/>
                            <ComboBoxItem Content="الطائف"/>
                            <ComboBoxItem Content="تبوك"/>
                            <ComboBoxItem Content="أبها"/>
                            <ComboBoxItem Content="حائل"/>
                            <ComboBoxItem Content="الخبر"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الرمز البريدي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PostalCodeTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <TextBlock Text="العنوان التفصيلي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="60"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Financial Information -->
                <TextBlock Text="المعلومات المالية" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#17A2B8"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الرقم الضريبي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TaxNumberTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم السجل التجاري" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CommercialRegisterTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="حد الائتمان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CreditLimitTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="مدة السداد (أيام)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PaymentTermsTextBox"
                                Text="30"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Additional Information -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Status -->
                <CheckBox x:Name="IsActiveCheckBox"
                         Content="نشط"
                         IsChecked="True"
                         Margin="0,0,0,15"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,0,0,10"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
