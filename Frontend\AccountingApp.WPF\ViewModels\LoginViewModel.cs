using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AccountingApp.WPF.Services;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AccountingApp.WPF.ViewModels
{
    /// <summary>
    /// ViewModel تسجيل الدخول
    /// Login ViewModel
    /// </summary>
    public partial class LoginViewModel : BaseViewModel
    {
        private readonly AuthService _authService;

        [ObservableProperty]
        private string _username = string.Empty;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        private bool _rememberMe = false;

        [ObservableProperty]
        private string _errorMessage = string.Empty;

        [ObservableProperty]
        private bool _isLoginEnabled = true;

        public LoginViewModel(AuthService authService)
        {
            _authService = authService;
            Title = "تسجيل الدخول - نظام المحاسبة المالية";
            
            // تعيين القيم الافتراضية للتطوير
            Username = "admin";
            Password = "admin123";
        }

        /// <summary>
        /// أمر تسجيل الدخول
        /// Login Command
        /// </summary>
        [RelayCommand]
        private async Task LoginAsync()
        {
            await ExecuteAsync(async () =>
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                ErrorMessage = string.Empty;
                IsLoginEnabled = false;

                try
                {
                    // محاولة تسجيل الدخول
                    var success = await _authService.LoginAsync(Username, Password);

                    if (success)
                    {
                        // إغلاق نافذة تسجيل الدخول وفتح النافذة الرئيسية
                        await ShowMainWindowAsync();
                    }
                    else
                    {
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    }
                }
                catch (Exception ex)
                {
                    ErrorMessage = $"خطأ في تسجيل الدخول: {ex.Message}";
                }
                finally
                {
                    IsLoginEnabled = true;
                }
            }, "جاري تسجيل الدخول...");
        }

        /// <summary>
        /// أمر إلغاء تسجيل الدخول
        /// Cancel Command
        /// </summary>
        [RelayCommand]
        private void Cancel()
        {
            Application.Current.Shutdown();
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// Validate Input
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(Username))
            {
                ErrorMessage = "يرجى إدخال اسم المستخدم";
                return false;
            }

            if (string.IsNullOrWhiteSpace(Password))
            {
                ErrorMessage = "يرجى إدخال كلمة المرور";
                return false;
            }

            return true;
        }

        /// <summary>
        /// إظهار النافذة الرئيسية
        /// Show Main Window
        /// </summary>
        private async Task ShowMainWindowAsync()
        {
            await UpdateUIAsync(() =>
            {
                // إنشاء النافذة الرئيسية
                var mainWindow = new Views.MainWindow();
                
                // إغلاق نافذة تسجيل الدخول
                var loginWindow = Application.Current.MainWindow;
                Application.Current.MainWindow = mainWindow;
                
                mainWindow.Show();
                loginWindow?.Close();
            });
        }

        /// <summary>
        /// معالجة الأخطاء
        /// Handle Errors
        /// </summary>
        protected override async Task HandleErrorAsync(Exception ex)
        {
            ErrorMessage = ex.Message;
            IsLoginEnabled = true;
        }

        /// <summary>
        /// تنظيف الموارد
        /// Cleanup
        /// </summary>
        public void Cleanup()
        {
            Username = string.Empty;
            Password = string.Empty;
            ErrorMessage = string.Empty;
        }
    }
}
