using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Purchases
{
    public partial class CreatePurchaseInvoiceWindow : Window
    {
        private readonly PurchaseInvoiceViewModel _editInvoice;
        private readonly bool _isEdit;
        private ObservableCollection<PurchaseItemViewModel> _invoiceItems;

        public CreatePurchaseInvoiceWindow(PurchaseInvoiceViewModel editInvoice = null)
        {
            InitializeComponent();
            _editInvoice = editInvoice;
            _isEdit = editInvoice != null;
            
            InitializeInvoiceItems();
            
            if (_isEdit)
            {
                HeaderTextBlock.Text = "تعديل فاتورة مشتريات - Edit Purchase Invoice";
                SaveButton.Content = "تحديث";
                LoadInvoiceData();
            }
            else
            {
                GenerateInvoiceNumber();
                SetDefaultDueDate();
            }
        }

        private void InitializeInvoiceItems()
        {
            _invoiceItems = new ObservableCollection<PurchaseItemViewModel>();
            ItemsDataGrid.ItemsSource = _invoiceItems;
        }

        private void GenerateInvoiceNumber()
        {
            var today = DateTime.Today;
            var invoiceNumber = $"PUR-{today:yyyy}-{new Random().Next(1000, 9999)}";
            InvoiceNumberTextBox.Text = invoiceNumber;
        }

        private void SetDefaultDueDate()
        {
            // تعيين تاريخ استحقاق افتراضي (30 يوم من تاريخ الفاتورة)
            DueDatePicker.SelectedDate = DateTime.Today.AddDays(30);
        }

        private void LoadInvoiceData()
        {
            if (_editInvoice == null) return;

            InvoiceNumberTextBox.Text = _editInvoice.InvoiceNumber;
            InvoiceDatePicker.SelectedDate = _editInvoice.InvoiceDate;
            SupplierInvoiceNumberTextBox.Text = _editInvoice.SupplierInvoiceNumber;
            
            // تعيين المورد
            foreach (ComboBoxItem item in SupplierComboBox.Items)
            {
                if (item.Content.ToString() == _editInvoice.SupplierName)
                {
                    SupplierComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Tag?.ToString() == _editInvoice.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحميل أصناف الفاتورة (محاكاة)
            LoadSampleInvoiceItems();
        }

        private void LoadSampleInvoiceItems()
        {
            // محاكاة تحميل أصناف الفاتورة
            _invoiceItems.Clear();
            _invoiceItems.Add(new PurchaseItemViewModel
            {
                ItemCode = "ITM001",
                ItemName = "لابتوب ديل انسبايرون",
                Unit = "قطعة",
                Quantity = 5,
                UnitPrice = 2200,
                DiscountPercent = 3
            });
            _invoiceItems.Add(new PurchaseItemViewModel
            {
                ItemCode = "ITM002",
                ItemName = "كرسي مكتب",
                Unit = "قطعة",
                Quantity = 10,
                UnitPrice = 180,
                DiscountPercent = 5
            });
            
            CalculateTotals();
        }

        private void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن تحميل معلومات المورد هنا
        }

        private void AddSupplier_Click(object sender, RoutedEventArgs e)
        {
            var addSupplierWindow = new AddSupplierWindow();
            if (addSupplierWindow.ShowDialog() == true)
            {
                // إعادة تحميل قائمة الموردين
                MessageBox.Show("تم إضافة المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ItemCodeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ItemCodeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var itemCode = selectedItem.Tag?.ToString();
                
                // محاكاة تحميل بيانات الصنف
                switch (itemCode)
                {
                    case "ITM001":
                        ItemNameTextBox.Text = "لابتوب ديل انسبايرون";
                        UnitTextBox.Text = "قطعة";
                        UnitPriceTextBox.Text = "2200.00";
                        break;
                    case "ITM002":
                        ItemNameTextBox.Text = "كرسي مكتب";
                        UnitTextBox.Text = "قطعة";
                        UnitPriceTextBox.Text = "180.00";
                        break;
                    case "ITM003":
                        ItemNameTextBox.Text = "حزمة ورق A4";
                        UnitTextBox.Text = "حزمة";
                        UnitPriceTextBox.Text = "22.00";
                        break;
                    case "ITM004":
                        ItemNameTextBox.Text = "فأرة لاسلكية";
                        UnitTextBox.Text = "قطعة";
                        UnitPriceTextBox.Text = "18.00";
                        break;
                }
            }
        }

        private void AddItem_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة اختيار الأصناف
            MessageBox.Show("سيتم فتح نافذة اختيار الأصناف", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RemoveItem_Click(object sender, RoutedEventArgs e)
        {
            if (ItemsDataGrid.SelectedItem is PurchaseItemViewModel selectedItem)
            {
                _invoiceItems.Remove(selectedItem);
                CalculateTotals();
            }
            else
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddItemToGrid_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateItemInput())
                return;

            var newItem = new PurchaseItemViewModel
            {
                ItemCode = ItemCodeComboBox.Text,
                ItemName = ItemNameTextBox.Text,
                Unit = UnitTextBox.Text,
                Quantity = decimal.Parse(QuantityTextBox.Text),
                UnitPrice = decimal.Parse(UnitPriceTextBox.Text),
                DiscountPercent = decimal.Parse(DiscountPercentTextBox.Text)
            };

            _invoiceItems.Add(newItem);
            CalculateTotals();
            ClearItemInputs();
        }

        private bool ValidateItemInput()
        {
            if (ItemCodeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار صنف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(QuantityTextBox.Text, out var quantity) || quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(UnitPriceTextBox.Text, out var price) || price <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(DiscountPercentTextBox.Text, out var discount) || discount < 0 || discount > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة خصم صحيحة (0-100)", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void ClearItemInputs()
        {
            ItemCodeComboBox.SelectedItem = null;
            ItemNameTextBox.Text = "";
            UnitTextBox.Text = "";
            QuantityTextBox.Text = "1";
            UnitPriceTextBox.Text = "0.00";
            DiscountPercentTextBox.Text = "0";
        }

        private void CalculateTotals()
        {
            var subTotal = _invoiceItems.Sum(i => i.LineTotal);
            var totalDiscount = _invoiceItems.Sum(i => i.DiscountAmount);
            var taxAmount = subTotal * 0.15m; // ضريبة 15%
            var totalAmount = subTotal + taxAmount;

            SubTotalTextBox.Text = subTotal.ToString("F2");
            TotalDiscountTextBox.Text = totalDiscount.ToString("F2");
            TaxAmountTextBox.Text = taxAmount.ToString("F2");
            TotalAmountTextBox.Text = totalAmount.ToString("F2");
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInvoice())
                return;

            try
            {
                // محاكاة حفظ الفاتورة
                var message = _isEdit ? "تم تحديث فاتورة المشتريات بنجاح!" : "تم إنشاء فاتورة المشتريات بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            SaveButton_Click(sender, e);
            if (DialogResult == true)
            {
                MessageBox.Show("سيتم طباعة فاتورة المشتريات", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool ValidateInvoice()
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SupplierComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class PurchaseItemViewModel
    {
        public string ItemCode { get; set; } = "";
        public string ItemName { get; set; } = "";
        public string Unit { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercent { get; set; }
        
        public decimal DiscountAmount => (UnitPrice * Quantity) * (DiscountPercent / 100);
        public decimal LineTotal => (UnitPrice * Quantity) - DiscountAmount;
    }
}
