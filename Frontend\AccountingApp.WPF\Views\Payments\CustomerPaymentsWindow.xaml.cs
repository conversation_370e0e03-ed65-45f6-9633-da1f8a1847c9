using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;

namespace AccountingApp.WPF.Views.Payments
{
    public partial class CustomerPaymentsWindow : Window
    {
        private ObservableCollection<CustomerReceiptViewModel> _receipts;
        private CustomerReceiptViewModel _selectedReceipt;

        public CustomerPaymentsWindow()
        {
            InitializeComponent();
            SetDefaultDates();
            LoadSampleData();
        }

        private void SetDefaultDates()
        {
            FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Today;
        }

        private void LoadSampleData()
        {
            _receipts = new ObservableCollection<CustomerReceiptViewModel>
            {
                new CustomerReceiptViewModel
                {
                    ReceiptNumber = "REC-2024-001",
                    ReceiptDate = DateTime.Today,
                    CustomerName = "شركة الأعمال المتقدمة",
                    InvoiceNumber = "INV-2024-001",
                    Amount = 9775,
                    PaymentMethod = "تحويل بنكي",
                    ReferenceNumber = "TRF-123456",
                    Status = "Completed",
                    Notes = "دفعة كاملة"
                },
                new CustomerReceiptViewModel
                {
                    ReceiptNumber = "REC-2024-002",
                    ReceiptDate = DateTime.Today.AddDays(-1),
                    CustomerName = "مؤسسة التجارة الحديثة",
                    InvoiceNumber = "INV-2024-002",
                    Amount = 10000,
                    PaymentMethod = "نقدي",
                    ReferenceNumber = "",
                    Status = "Completed",
                    Notes = "دفعة جزئية"
                },
                new CustomerReceiptViewModel
                {
                    ReceiptNumber = "REC-2024-003",
                    ReceiptDate = DateTime.Today.AddDays(-2),
                    CustomerName = "شركة الخدمات المتكاملة",
                    InvoiceNumber = "INV-2024-003",
                    Amount = 6325,
                    PaymentMethod = "شيك",
                    ReferenceNumber = "CHK-789012",
                    Status = "Pending",
                    Notes = "في انتظار تحصيل الشيك"
                },
                new CustomerReceiptViewModel
                {
                    ReceiptNumber = "REC-2024-004",
                    ReceiptDate = DateTime.Today.AddDays(-3),
                    CustomerName = "مكتب الاستشارات القانونية",
                    InvoiceNumber = "INV-2024-004",
                    Amount = 3680,
                    PaymentMethod = "تحويل بنكي",
                    ReferenceNumber = "TRF-345678",
                    Status = "Completed",
                    Notes = "دفعة كاملة"
                },
                new CustomerReceiptViewModel
                {
                    ReceiptNumber = "REC-2024-005",
                    ReceiptDate = DateTime.Today.AddDays(-4),
                    CustomerName = "شركة التطوير العقاري",
                    InvoiceNumber = "INV-2024-005",
                    Amount = 13800,
                    PaymentMethod = "نقدي",
                    ReferenceNumber = "",
                    Status = "Completed",
                    Notes = "دفعة كاملة"
                }
            };

            ReceiptsDataGrid.ItemsSource = _receipts;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleReceipts = ReceiptsDataGrid.ItemsSource as ObservableCollection<CustomerReceiptViewModel> ?? _receipts;
            
            ReceiptCountTextBlock.Text = visibleReceipts.Count.ToString();
            TotalReceiptsTextBlock.Text = visibleReceipts.Sum(r => r.Amount).ToString("F2");
            CashReceiptsTextBlock.Text = visibleReceipts.Where(r => r.PaymentMethod == "نقدي").Sum(r => r.Amount).ToString("F2");
            BankReceiptsTextBlock.Text = visibleReceipts.Where(r => r.PaymentMethod == "تحويل بنكي").Sum(r => r.Amount).ToString("F2");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddDays(-30);
            var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;
            var selectedCustomer = CustomerFilterComboBox.SelectedItem?.ToString() ?? "جميع العملاء";

            var filteredReceipts = _receipts.Where(r =>
                r.ReceiptDate >= fromDate &&
                r.ReceiptDate <= toDate &&
                (selectedCustomer == "جميع العملاء" || r.CustomerName == selectedCustomer)
            ).ToList();

            ReceiptsDataGrid.ItemsSource = new ObservableCollection<CustomerReceiptViewModel>(filteredReceipts);
            UpdateSummary();
            StatusTextBlock.Text = "تم تطبيق الفلاتر";
        }

        private void NewReceipt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var createReceiptWindow = new CreateReceiptWindow();
                if (createReceiptWindow.ShowDialog() == true)
                {
                    LoadSampleData(); // تحديث البيانات
                    StatusTextBlock.Text = "تم إضافة مقبوض جديد";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء مقبوض جديد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار مقبوض للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editReceiptWindow = new CreateReceiptWindow(_selectedReceipt);
                if (editReceiptWindow.ShowDialog() == true)
                {
                    LoadSampleData(); // تحديث البيانات
                    StatusTextBlock.Text = "تم تعديل المقبوض";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المقبوض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار مقبوض للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف المقبوض '{_selectedReceipt.ReceiptNumber}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _receipts.Remove(_selectedReceipt);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف المقبوض";
            }
        }

        private void PrintReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار مقبوض للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show($"سيتم طباعة المقبوض {_selectedReceipt.ReceiptNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "تم إرسال المقبوض للطباعة";
        }

        private void ReceiptsDataGrid_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            _selectedReceipt = ReceiptsDataGrid.SelectedItem as CustomerReceiptViewModel;
            
            if (_selectedReceipt != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedReceipt.ReceiptNumber} - {_selectedReceipt.CustomerName}";
            }
        }
    }

    public class CustomerReceiptViewModel
    {
        public string ReceiptNumber { get; set; } = "";
        public DateTime ReceiptDate { get; set; }
        public string CustomerName { get; set; } = "";
        public string InvoiceNumber { get; set; } = "";
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = "";
        public string ReferenceNumber { get; set; } = "";
        public string Status { get; set; } = "";
        public string Notes { get; set; } = "";

        public string StatusText => Status switch
        {
            "Completed" => "مكتمل",
            "Pending" => "معلق",
            "Cancelled" => "ملغي",
            _ => "غير محدد"
        };

        public string StatusColor => Status switch
        {
            "Completed" => "Green",
            "Pending" => "Orange",
            "Cancelled" => "Red",
            _ => "Black"
        };
    }
}
