using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Inventory
{
    public partial class UnitsWindow : Window
    {
        private ObservableCollection<UnitViewModel> _units;
        private UnitViewModel _selectedUnit;

        public UnitsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _units = new ObservableCollection<UnitViewModel>
            {
                new UnitViewModel
                {
                    UnitCode = "UNIT001",
                    Name = "Piece",
                    NameAr = "قطعة",
                    Symbol = "pcs",
                    UnitType = "Count",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT002",
                    Name = "Kilogram",
                    NameAr = "كيلو",
                    Symbol = "kg",
                    UnitType = "Weight",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT003",
                    Name = "Meter",
                    NameAr = "متر",
                    Symbol = "m",
                    UnitType = "Length",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT004",
                    Name = "Liter",
                    NameAr = "لتر",
                    Symbol = "L",
                    UnitType = "Volume",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT005",
                    Name = "Pack",
                    NameAr = "حزمة",
                    Symbol = "pack",
                    UnitType = "Count",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT006",
                    Name = "Box",
                    NameAr = "صندوق",
                    Symbol = "box",
                    UnitType = "Count",
                    IsActive = true
                },
                new UnitViewModel
                {
                    UnitCode = "UNIT007",
                    Name = "Hour",
                    NameAr = "ساعة",
                    Symbol = "hr",
                    UnitType = "Time",
                    IsActive = true
                }
            };

            UnitsDataGrid.ItemsSource = _units;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            UnitCountTextBlock.Text = $"عدد الوحدات: {_units.Count}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddUnit_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير إضافة الوحدات قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditUnit_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedUnit == null)
            {
                MessageBox.Show("يرجى اختيار وحدة للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("سيتم تطوير تعديل الوحدات قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteUnit_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedUnit == null)
            {
                MessageBox.Show("يرجى اختيار وحدة للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الوحدة '{_selectedUnit.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _units.Remove(_selectedUnit);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف الوحدة بنجاح";
            }
        }

        private void UnitsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedUnit = UnitsDataGrid.SelectedItem as UnitViewModel;
            
            if (_selectedUnit != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedUnit.UnitCode} - {_selectedUnit.Name}";
            }
        }
    }

    public class UnitViewModel
    {
        public string UnitCode { get; set; } = "";
        public string Name { get; set; } = "";
        public string NameAr { get; set; } = "";
        public string Symbol { get; set; } = "";
        public string UnitType { get; set; } = "";
        public bool IsActive { get; set; }
        
        public string UnitTypeText => UnitType switch
        {
            "Count" => "عدد",
            "Weight" => "وزن",
            "Length" => "طول",
            "Volume" => "حجم",
            "Time" => "وقت",
            _ => "أخرى"
        };
        
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
    }
}
