"""
API المصادقة والمستخدمين
Authentication and Users API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List

from database.config import get_db
from core.schemas import (
    Token, UserResponse, UserCreate, UserUpdate, MessageResponse
)
from core.security import get_current_user, get_current_admin_user
from services.auth_service import AuthService
from models.users.user import User

router = APIRouter()

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    تسجيل الدخول
    User login endpoint
    """
    auth_service = AuthService(db)
    
    # مصادقة المستخدم
    user = auth_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # إنشاء رمز الوصول
    access_token = auth_service.create_access_token_for_user(user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على معلومات المستخدم الحالي
    Get current user information
    """
    return current_user

@router.post("/users", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إنشاء مستخدم جديد (للمديرين فقط)
    Create new user (admin only)
    """
    auth_service = AuthService(db)
    return auth_service.create_user(user_data)

@router.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    الحصول على قائمة المستخدمين (للمديرين فقط)
    Get users list (admin only)
    """
    auth_service = AuthService(db)
    
    if active_only:
        return auth_service.get_active_users(skip, limit)
    else:
        return auth_service.get_all_users(skip, limit)

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    الحصول على مستخدم محدد (للمديرين فقط)
    Get specific user (admin only)
    """
    auth_service = AuthService(db)
    user = auth_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تحديث بيانات المستخدم (للمديرين فقط)
    Update user data (admin only)
    """
    auth_service = AuthService(db)
    user = auth_service.update_user(user_id, user_data)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.put("/users/{user_id}/deactivate", response_model=MessageResponse)
async def deactivate_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إلغاء تفعيل المستخدم (للمديرين فقط)
    Deactivate user (admin only)
    """
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate yourself"
        )
    
    auth_service = AuthService(db)
    success = auth_service.deactivate_user(user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return MessageResponse(
        message="User deactivated successfully",
        message_ar="تم إلغاء تفعيل المستخدم بنجاح"
    )

@router.put("/users/{user_id}/activate", response_model=MessageResponse)
async def activate_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تفعيل المستخدم (للمديرين فقط)
    Activate user (admin only)
    """
    auth_service = AuthService(db)
    success = auth_service.activate_user(user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return MessageResponse(
        message="User activated successfully",
        message_ar="تم تفعيل المستخدم بنجاح"
    )

@router.post("/change-password", response_model=MessageResponse)
async def change_password(
    old_password: str,
    new_password: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تغيير كلمة المرور
    Change password
    """
    auth_service = AuthService(db)
    success = auth_service.change_password(current_user.id, old_password, new_password)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid old password"
        )
    
    return MessageResponse(
        message="Password changed successfully",
        message_ar="تم تغيير كلمة المرور بنجاح"
    )

@router.get("/users/search/{search_term}", response_model=List[UserResponse])
async def search_users(
    search_term: str,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    البحث في المستخدمين (للمديرين فقط)
    Search users (admin only)
    """
    auth_service = AuthService(db)
    return auth_service.search_users(search_term, skip, limit)
