<Window x:Class="AccountingApp.WPF.Views.Accounting.ChartOfAccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="دليل الحسابات - Chart of Accounts"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="دليل الحسابات - Chart of Accounts"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="إضافة حساب جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddAccount_Click"/>
                
                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditAccount_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteAccount_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="200"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>
                
                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Accounts TreeView -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <TreeView x:Name="AccountsTreeView"
                     FontSize="12"
                     SelectedItemChanged="AccountsTreeView_SelectedItemChanged">
                <TreeView.ItemTemplate>
                    <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="{Binding AccountNumber}" 
                                     FontWeight="Bold" 
                                     Foreground="#007ACC"
                                     Width="80"/>
                            <TextBlock Text=" - " Margin="5,0"/>
                            <TextBlock Text="{Binding Name}" 
                                     FontWeight="SemiBold"/>
                            <TextBlock Text=" (" Margin="5,0,0,0"/>
                            <TextBlock Text="{Binding NameAr}" 
                                     Foreground="#6C757D"/>
                            <TextBlock Text=")"/>
                            <TextBlock Text="{Binding Balance, StringFormat=C}" 
                                     Foreground="{Binding BalanceColor}"
                                     FontWeight="Bold"
                                     Margin="20,0,0,0"/>
                        </StackPanel>
                    </HierarchicalDataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر حساب للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <TextBlock x:Name="AccountCountTextBlock"
                          Text="عدد الحسابات: 0"
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
