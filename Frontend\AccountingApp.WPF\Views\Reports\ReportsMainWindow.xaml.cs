using System;
using System.Windows;

namespace AccountingApp.WPF.Views.Reports
{
    public partial class ReportsMainWindow : Window
    {
        public ReportsMainWindow()
        {
            InitializeComponent();
            LoadQuickStats();
        }

        private void LoadQuickStats()
        {
            // محاكاة تحميل الإحصائيات السريعة
            TotalSalesTextBlock.Text = "245,500 ريال";
            TotalPurchasesTextBlock.Text = "156,200 ريال";
            NetProfitTextBlock.Text = "89,300 ريال";
            InventoryValueTextBlock.Text = "125,500 ريال";

            StatusTextBlock.Text = "تم تحميل الإحصائيات بنجاح";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // Sales Reports
        private void DailySalesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new SalesReportWindow("daily");
                reportWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح تقرير المبيعات اليومية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MonthlySalesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new SalesReportWindow("monthly");
                reportWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح تقرير المبيعات الشهرية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TopCustomersReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new CustomersReportWindow();
                reportWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح تقرير أفضل العملاء";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TopItemsReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الأصناف الأكثر مبيعاً");
        }

        private void SalesByCategoryReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير المبيعات حسب الفئة");
        }

        private void SalesReturnsReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير مرتجعات المبيعات");
        }

        // Purchase Reports
        private void DailyPurchasesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new PurchasesReportWindow("daily");
                reportWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح تقرير المشتريات اليومية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MonthlyPurchasesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new PurchasesReportWindow("monthly");
                reportWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح تقرير المشتريات الشهرية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TopSuppliersReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير أفضل الموردين");
        }

        private void PurchasesByCategoryReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير المشتريات حسب الفئة");
        }

        private void SuppliersBalanceReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير أرصدة الموردين");
        }

        private void PurchaseReturnsReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير مرتجعات المشتريات");
        }

        // Financial Reports
        private void IncomeStatementReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("قائمة الدخل");
        }

        private void BalanceSheetReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("الميزانية العمومية");
        }

        private void CashFlowReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("قائمة التدفقات النقدية");
        }

        private void ProfitLossReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الأرباح والخسائر");
        }

        private void TaxReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الضرائب");
        }

        private void ExpensesReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير المصروفات");
        }

        // Inventory Reports
        private void InventoryStatusReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير حالة المخزون");
        }

        private void LowStockReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الأصناف منخفضة المخزون");
        }

        private void InventoryMovementReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير حركة المخزون");
        }

        private void InventoryValuationReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير تقييم المخزون");
        }

        private void SlowMovingItemsReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الأصناف الراكدة");
        }

        private void StockTakeReport_Click(object sender, RoutedEventArgs e)
        {
            ShowComingSoonMessage("تقرير الجرد");
        }

        private void ShowComingSoonMessage(string reportName)
        {
            MessageBox.Show($"سيتم تطوير {reportName} قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = $"تم طلب {reportName} - قيد التطوير";
        }
    }
}
