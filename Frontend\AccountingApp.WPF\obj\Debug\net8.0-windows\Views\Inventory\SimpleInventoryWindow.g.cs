﻿#pragma checksum "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D4783DE035A890A3D91A990EC0AA44D35A08865C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Inventory {
    
    
    /// <summary>
    /// SimpleInventoryWindow
    /// </summary>
    public partial class SimpleInventoryWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 98 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockItemsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalValueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox RecentItemsListBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/inventory/simpleinventorywindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 31 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 51 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageItems_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 60 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageCategories_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 69 "..\..\..\..\..\Views\Inventory\SimpleInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageUnits_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TotalItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.LowStockItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TotalValueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.RecentItemsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 9:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

