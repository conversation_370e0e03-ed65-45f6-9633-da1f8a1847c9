<Window x:Class="AccountingApp.WPF.Views.Purchases.PurchaseInvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="فواتير المشتريات - Purchase Invoices"
        Height="750" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#DC3545" Padding="15">
            <Grid>
                <TextBlock Text="فواتير المشتريات - Purchase Invoices"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="فاتورة جديدة"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="NewInvoice_Click"/>
                
                <Button Content="عرض/تعديل"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="ViewEditInvoice_Click"/>
                
                <Button Content="طباعة"
                       Background="#6F42C1"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="PrintInvoice_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteInvoice_Click"/>
                
                <Separator Margin="10,0"/>
                
                <Button Content="إدارة الموردين"
                       Background="#17A2B8"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="ManageSuppliers_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="5"/>
                <DatePicker x:Name="FromDatePicker"
                           Width="120"
                           Margin="5"
                           SelectedDate="{x:Static sys:DateTime.Today}"
                           SelectedDateChanged="DateFilter_Changed"/>
                
                <TextBlock Text="إلى تاريخ:" VerticalAlignment="Center" Margin="5"/>
                <DatePicker x:Name="ToDatePicker"
                           Width="120"
                           Margin="5"
                           SelectedDate="{x:Static sys:DateTime.Today}"
                           SelectedDateChanged="DateFilter_Changed"/>
                
                <ComboBox x:Name="StatusFilterComboBox"
                         Width="120"
                         Margin="5"
                         SelectionChanged="StatusFilter_Changed">
                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    <ComboBoxItem Content="مسودة"/>
                    <ComboBoxItem Content="مؤكدة"/>
                    <ComboBoxItem Content="مدفوعة"/>
                    <ComboBoxItem Content="ملغاة"/>
                </ComboBox>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="150"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>
                
                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Invoices DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="InvoicesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="InvoicesDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                    <DataGridTextColumn Header="رقم فاتورة المورد" Binding="{Binding SupplierInvoiceNumber}" Width="140"/>
                    <DataGridTextColumn Header="عدد الأصناف" Binding="{Binding ItemsCount}" Width="100"/>
                    <DataGridTextColumn Header="المبلغ قبل الضريبة" Binding="{Binding SubTotal, StringFormat=C}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Blue"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat=C}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Orange"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalAmount, StringFormat=C}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Red"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المدفوع" Binding="{Binding PaidAmount, StringFormat=C}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Purple"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat=C}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="{Binding RemainingColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المستخدم" Binding="{Binding CreatedBy}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر فاتورة للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="InvoiceCountTextBlock" Text="عدد الفواتير: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalPurchasesTextBlock" Text="إجمالي المشتريات: 0.00" Foreground="Red" FontWeight="Bold" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalTaxTextBlock" Text="إجمالي الضرائب: 0.00" Foreground="Orange" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
