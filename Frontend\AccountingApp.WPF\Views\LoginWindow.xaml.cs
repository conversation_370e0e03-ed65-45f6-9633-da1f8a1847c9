using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AccountingApp.WPF.ViewModels;
using AccountingApp.WPF.Services;

namespace AccountingApp.WPF.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// Login Window
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;

        public LoginWindow()
        {
            InitializeComponent();
            
            // إنشاء الخدمات
            var apiService = new ApiService();
            var authService = new AuthService(apiService);
            
            // إنشاء ViewModel
            _viewModel = new LoginViewModel(authService);
            DataContext = _viewModel;

            // ربط كلمة المرور
            PasswordBox.PasswordChanged += PasswordBox_PasswordChanged;
            
            // التركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // معالجة مفتاح Enter
            KeyDown += LoginWindow_KeyDown;
        }

        /// <summary>
        /// معالجة تغيير كلمة المرور
        /// Handle Password Change
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.Password = passwordBox.Password;
            }
        }

        /// <summary>
        /// معالجة ضغط المفاتيح
        /// Handle Key Press
        /// </summary>
        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (_viewModel.LoginCommand.CanExecute(null))
                {
                    _viewModel.LoginCommand.Execute(null);
                }
            }
            else if (e.Key == Key.Escape)
            {
                if (_viewModel.CancelCommand.CanExecute(null))
                {
                    _viewModel.CancelCommand.Execute(null);
                }
            }
        }

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// Cleanup on Window Close
        /// </summary>
        protected override void OnClosed(System.EventArgs e)
        {
            _viewModel?.Cleanup();
            base.OnClosed(e);
        }
    }
}
