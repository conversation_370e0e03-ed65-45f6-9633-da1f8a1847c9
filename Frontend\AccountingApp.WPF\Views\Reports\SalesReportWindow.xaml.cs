using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;

namespace AccountingApp.WPF.Views.Reports
{
    public partial class SalesReportWindow : Window
    {
        private readonly string _reportType;
        private ObservableCollection<SalesReportItem> _salesData;

        public SalesReportWindow(string reportType = "daily")
        {
            InitializeComponent();
            _reportType = reportType;
            
            SetupWindow();
            SetDefaultDates();
            LoadSampleData();
        }

        private void SetupWindow()
        {
            switch (_reportType)
            {
                case "daily":
                    HeaderTextBlock.Text = "تقرير المبيعات اليومية - Daily Sales Report";
                    Title = "تقرير المبيعات اليومية";
                    break;
                case "monthly":
                    HeaderTextBlock.Text = "تقرير المبيعات الشهرية - Monthly Sales Report";
                    Title = "تقرير المبيعات الشهرية";
                    break;
                default:
                    HeaderTextBlock.Text = "تقرير المبيعات - Sales Report";
                    break;
            }
        }

        private void SetDefaultDates()
        {
            switch (_reportType)
            {
                case "daily":
                    FromDatePicker.SelectedDate = DateTime.Today;
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
                case "monthly":
                    FromDatePicker.SelectedDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
                default:
                    FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
            }
        }

        private void LoadSampleData()
        {
            _salesData = new ObservableCollection<SalesReportItem>
            {
                new SalesReportItem
                {
                    InvoiceNumber = "INV-2024-001",
                    InvoiceDate = DateTime.Today,
                    CustomerName = "شركة الأعمال المتقدمة",
                    SubTotal = 8500,
                    TaxAmount = 1275,
                    TotalAmount = 9775,
                    PaidAmount = 9775,
                    RemainingAmount = 0,
                    Status = "Paid"
                },
                new SalesReportItem
                {
                    InvoiceNumber = "INV-2024-002",
                    InvoiceDate = DateTime.Today.AddDays(-1),
                    CustomerName = "مؤسسة التجارة الحديثة",
                    SubTotal = 15000,
                    TaxAmount = 2250,
                    TotalAmount = 17250,
                    PaidAmount = 10000,
                    RemainingAmount = 7250,
                    Status = "PartiallyPaid"
                },
                new SalesReportItem
                {
                    InvoiceNumber = "INV-2024-003",
                    InvoiceDate = DateTime.Today.AddDays(-2),
                    CustomerName = "شركة الخدمات المتكاملة",
                    SubTotal = 5500,
                    TaxAmount = 825,
                    TotalAmount = 6325,
                    PaidAmount = 0,
                    RemainingAmount = 6325,
                    Status = "Pending"
                },
                new SalesReportItem
                {
                    InvoiceNumber = "INV-2024-004",
                    InvoiceDate = DateTime.Today.AddDays(-3),
                    CustomerName = "مكتب الاستشارات القانونية",
                    SubTotal = 3200,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 3680,
                    RemainingAmount = 0,
                    Status = "Paid"
                },
                new SalesReportItem
                {
                    InvoiceNumber = "INV-2024-005",
                    InvoiceDate = DateTime.Today.AddDays(-4),
                    CustomerName = "شركة التطوير العقاري",
                    SubTotal = 12000,
                    TaxAmount = 1800,
                    TotalAmount = 13800,
                    PaidAmount = 13800,
                    RemainingAmount = 0,
                    Status = "Paid"
                }
            };

            SalesDataGrid.ItemsSource = _salesData;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleData = SalesDataGrid.ItemsSource as ObservableCollection<SalesReportItem> ?? _salesData;
            
            InvoiceCountTextBlock.Text = visibleData.Count.ToString();
            TotalSalesTextBlock.Text = visibleData.Sum(s => s.TotalAmount).ToString("F2");
            TotalTaxTextBlock.Text = visibleData.Sum(s => s.TaxAmount).ToString("F2");
            TotalPaidTextBlock.Text = visibleData.Sum(s => s.PaidAmount).ToString("F2");
            TotalRemainingTextBlock.Text = visibleData.Sum(s => s.RemainingAmount).ToString("F2");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddDays(-30);
            var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;
            var selectedCustomer = CustomerFilterComboBox.SelectedItem?.ToString() ?? "جميع العملاء";

            var filteredData = _salesData.Where(s =>
                s.InvoiceDate >= fromDate &&
                s.InvoiceDate <= toDate &&
                (selectedCustomer == "جميع العملاء" || s.CustomerName == selectedCustomer)
            ).ToList();

            SalesDataGrid.ItemsSource = new ObservableCollection<SalesReportItem>(filteredData);
            UpdateSummary();
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة التقرير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير إلى Excel", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToPDF_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير إلى PDF", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    public class SalesReportItem
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; } = "";
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; } = "";

        public string StatusText => Status switch
        {
            "Paid" => "مدفوعة",
            "PartiallyPaid" => "مدفوعة جزئياً",
            "Pending" => "معلقة",
            "Cancelled" => "ملغاة",
            _ => "غير محدد"
        };

        public string StatusColor => Status switch
        {
            "Paid" => "Green",
            "PartiallyPaid" => "Orange",
            "Pending" => "Red",
            "Cancelled" => "Gray",
            _ => "Black"
        };

        public string RemainingAmountColor => RemainingAmount > 0 ? "Red" : "Green";
    }
}
