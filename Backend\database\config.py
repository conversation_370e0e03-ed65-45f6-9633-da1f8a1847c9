"""
إعدادات قاعدة البيانات
Database Configuration
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعدادات قاعدة البيانات
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "sqlite:///./accounting_app.db"  # SQLite كافتراضي
)

# إنشاء محرك قاعدة البيانات
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {},
    echo=True  # لعرض استعلامات SQL في وضع التطوير
)

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# قاعدة النماذج
Base = declarative_base()

def get_db():
    """
    الحصول على جلسة قاعدة البيانات
    Get database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """
    إنشاء جداول قاعدة البيانات
    Create database tables
    """
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """
    حذف جداول قاعدة البيانات
    Drop database tables
    """
    Base.metadata.drop_all(bind=engine)
