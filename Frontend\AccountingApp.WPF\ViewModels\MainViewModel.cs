using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AccountingApp.WPF.Services;
using AccountingApp.WPF.Models;
using System;
using System.Threading.Tasks;
using System.Windows;
using Timer = System.Timers.Timer;
using System.Timers;

namespace AccountingApp.WPF.ViewModels
{
    /// <summary>
    /// ViewModel النافذة الرئيسية
    /// Main Window ViewModel
    /// </summary>
    public partial class MainViewModel : BaseViewModel
    {
        private readonly AuthService _authService;
        private readonly Timer _clockTimer;

        [ObservableProperty]
        private User? _currentUser;

        [ObservableProperty]
        private string _currentDateTime = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");

        [ObservableProperty]
        private bool _isConnected = true;

        public MainViewModel(AuthService authService)
        {
            _authService = authService;
            Title = "نظام المحاسبة المالية المتكامل";

            // تحديث معلومات المستخدم الحالي
            CurrentUser = _authService.CurrentUser;

            // إعداد مؤقت الساعة
            _clockTimer = new Timer(1000); // كل ثانية
            _clockTimer.Elapsed += UpdateDateTime;
            _clockTimer.Start();

            // فحص الاتصال بالخادم
            _ = CheckConnectionAsync();
        }

        /// <summary>
        /// أمر تسجيل الخروج
        /// Logout Command
        /// </summary>
        [RelayCommand]
        private async Task LogoutAsync()
        {
            var confirmed = await ShowConfirmationAsync(
                "هل تريد تسجيل الخروج من النظام؟",
                "تأكيد تسجيل الخروج");

            if (confirmed)
            {
                // تسجيل الخروج
                _authService.Logout();

                // إظهار نافذة تسجيل الدخول
                await UpdateUIAsync(() =>
                {
                    var loginWindow = new Views.LoginWindow();
                    var mainWindow = Application.Current.MainWindow;

                    Application.Current.MainWindow = loginWindow;
                    loginWindow.Show();
                    mainWindow?.Close();
                });
            }
        }

        /// <summary>
        /// أمر إظهار دليل الحسابات
        /// Show Chart of Accounts Command
        /// </summary>
        [RelayCommand]
        private async Task ShowChartOfAccountsAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة دليل الحسابات
                await ShowSuccessAsync("سيتم تطبيق صفحة دليل الحسابات قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار القيود اليومية
        /// Show Journal Entries Command
        /// </summary>
        [RelayCommand]
        private async Task ShowJournalEntriesAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة القيود اليومية
                await ShowSuccessAsync("سيتم تطبيق صفحة القيود اليومية قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار إدارة العملاء
        /// Show Customer Management Command
        /// </summary>
        [RelayCommand]
        private async Task ShowCustomerManagementAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة إدارة العملاء
                await ShowSuccessAsync("سيتم تطبيق صفحة إدارة العملاء قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار إدارة الموردين
        /// Show Supplier Management Command
        /// </summary>
        [RelayCommand]
        private async Task ShowSupplierManagementAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة إدارة الموردين
                await ShowSuccessAsync("سيتم تطبيق صفحة إدارة الموردين قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار إدارة الأصناف
        /// Show Item Management Command
        /// </summary>
        [RelayCommand]
        private async Task ShowItemManagementAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة إدارة الأصناف
                await ShowSuccessAsync("سيتم تطبيق صفحة إدارة الأصناف قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار التقارير المالية
        /// Show Financial Reports Command
        /// </summary>
        [RelayCommand]
        private async Task ShowFinancialReportsAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة التقارير المالية
                await ShowSuccessAsync("سيتم تطبيق صفحة التقارير المالية قريباً");
            });
        }

        /// <summary>
        /// أمر إظهار الإعدادات
        /// Show Settings Command
        /// </summary>
        [RelayCommand]
        private async Task ShowSettingsAsync()
        {
            await ExecuteAsync(async () =>
            {
                // TODO: تطبيق صفحة الإعدادات
                await ShowSuccessAsync("سيتم تطبيق صفحة الإعدادات قريباً");
            });
        }

        /// <summary>
        /// تحديث الوقت والتاريخ
        /// Update Date and Time
        /// </summary>
        private void UpdateDateTime(object? sender, ElapsedEventArgs e)
        {
            UpdateUI(() =>
            {
                CurrentDateTime = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
            });
        }

        /// <summary>
        /// فحص الاتصال بالخادم
        /// Check Server Connection
        /// </summary>
        private async Task CheckConnectionAsync()
        {
            try
            {
                // TODO: تطبيق فحص الاتصال الفعلي
                IsConnected = true;
            }
            catch
            {
                IsConnected = false;
            }
        }

        /// <summary>
        /// تحديث معلومات المستخدم
        /// Refresh User Information
        /// </summary>
        public async Task RefreshUserInfoAsync()
        {
            await ExecuteAsync(async () =>
            {
                await _authService.RefreshUserInfoAsync();
                CurrentUser = _authService.CurrentUser;
            });
        }

        /// <summary>
        /// تنظيف الموارد
        /// Cleanup Resources
        /// </summary>
        public void Cleanup()
        {
            _clockTimer?.Stop();
            _clockTimer?.Dispose();
        }
    }
}
