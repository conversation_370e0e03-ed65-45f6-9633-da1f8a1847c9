<Window x:Class="AccountingApp.WPF.Views.Accounting.AddJournalEntryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="إضافة قيد يومي - Add Journal Entry"
        Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة قيد يومي - Add Journal Entry"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Entry Info -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="رقم القيد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="EntryNumberTextBox" Padding="5" IsReadOnly="True" Background="#E9ECEF"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Margin="10,0">
                    <TextBlock Text="التاريخ *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="EntryDatePicker" Padding="5" SelectedDate="{x:Static sys:DateTime.Today}"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Margin="10,0">
                    <TextBlock Text="المرجع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ReferenceTextBox" Padding="5"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                    <TextBlock Text="نوع القيد" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="EntryTypeComboBox" Padding="5">
                        <ComboBoxItem Content="عام - General" IsSelected="True"/>
                        <ComboBoxItem Content="مبيعات - Sales"/>
                        <ComboBoxItem Content="مشتريات - Purchases"/>
                        <ComboBoxItem Content="نقدية - Cash"/>
                    </ComboBox>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Entry Lines -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Description -->
                <StackPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock Text="وصف القيد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="DescriptionTextBox" Padding="5" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                </StackPanel>

                <!-- Toolbar -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <Button Content="إضافة سطر"
                           Background="#28A745"
                           Foreground="White"
                           Padding="10,5"
                           Margin="5,0"
                           Click="AddLine_Click"/>

                    <Button Content="حذف سطر"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="10,5"
                           Margin="5,0"
                           Click="DeleteLine_Click"/>
                </StackPanel>

                <!-- Entry Lines DataGrid -->
                <DataGrid Grid.Row="2"
                         x:Name="EntryLinesDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         GridLinesVisibility="All"
                         HeadersVisibility="Column"
                         FontSize="12">

                    <DataGrid.Columns>
                        <DataGridComboBoxColumn Header="الحساب *" Width="200" SelectedValueBinding="{Binding AccountId}">
                            <DataGridComboBoxColumn.ElementStyle>
                                <Style TargetType="ComboBox">
                                    <Setter Property="ItemsSource" Value="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=Accounts}"/>
                                    <Setter Property="DisplayMemberPath" Value="DisplayName"/>
                                    <Setter Property="SelectedValuePath" Value="AccountNumber"/>
                                </Style>
                            </DataGridComboBoxColumn.ElementStyle>
                        </DataGridComboBoxColumn>

                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="مدين" Binding="{Binding DebitAmount, StringFormat=F2}" Width="100"/>
                        <DataGridTextColumn Header="دائن" Binding="{Binding CreditAmount, StringFormat=F2}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Totals -->
        <Border Grid.Row="3" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="ErrorTextBlock"
                          Grid.Column="0"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          VerticalAlignment="Center"
                          Visibility="Collapsed"/>

                <TextBlock Grid.Column="1" Text="إجمالي المدين:" FontWeight="SemiBold" Margin="10,0,5,0" VerticalAlignment="Center"/>
                <TextBlock x:Name="TotalDebitTextBlock" Grid.Column="2" Text="0.00" FontWeight="Bold" Foreground="Blue" Margin="0,0,20,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Column="3" Text="إجمالي الدائن:" FontWeight="SemiBold" Margin="10,0,5,0" VerticalAlignment="Center"/>
                <TextBlock x:Name="TotalCreditTextBlock" Grid.Column="4" Text="0.00" FontWeight="Bold" Foreground="Green" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="4" Background="#F8F9FA" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>

                <Button Content="حفظ وترحيل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveAndPostButton_Click"/>

                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
