<Window x:Class="AccountingApp.WPF.Views.Finance.CashMovementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الحركات النقدية - Cash Movements"
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FFC107" Padding="15">
            <TextBlock Text="الحركات النقدية - Cash Movements"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="Black"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <StackPanel Grid.Row="1" Margin="20" VerticalAlignment="Center">
            <!-- Movement Type -->
            <TextBlock Text="نوع الحركة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="MovementTypeComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15">
                <ComboBoxItem Content="إيداع - Deposit" Tag="Deposit" IsSelected="True"/>
                <ComboBoxItem Content="سحب - Withdrawal" Tag="Withdrawal"/>
            </ComboBox>

            <!-- Cash Account -->
            <TextBlock Text="الحساب النقدي *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="CashAccountComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15">
                <ComboBoxItem Content="CASH001 - الصندوق الرئيسي"/>
                <ComboBoxItem Content="CASH002 - صندوق الفرع الثاني"/>
            </ComboBox>

            <!-- Amount -->
            <TextBlock Text="المبلغ *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="AmountTextBox"
                    Text="0.00"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"/>

            <!-- Description -->
            <TextBlock Text="الوصف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="DescriptionTextBox"
                    Padding="8"
                    FontSize="14"
                    Height="60"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    Margin="0,0,0,15"/>

            <!-- Reference -->
            <TextBlock Text="المرجع" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="ReferenceTextBox"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"/>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorTextBlock"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      Margin="0,0,0,10"
                      Visibility="Collapsed"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تنفيذ الحركة"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="ExecuteButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
