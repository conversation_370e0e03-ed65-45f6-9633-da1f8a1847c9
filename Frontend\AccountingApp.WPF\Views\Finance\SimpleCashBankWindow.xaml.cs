using System;
using System.Windows;

namespace AccountingApp.WPF.Views.Finance
{
    public partial class SimpleCashBankWindow : Window
    {
        public SimpleCashBankWindow()
        {
            InitializeComponent();
            LoadSummaryData();
        }

        private void LoadSummaryData()
        {
            // محاكاة تحميل بيانات ملخص الحسابات المالية
            CashBalanceTextBlock.Text = "25,500 ريال";
            BankBalanceTextBlock.Text = "185,750 ريال";
            TotalBalanceTextBlock.Text = "211,250 ريال";
            
            StatusTextBlock.Text = "تم تحميل البيانات بنجاح";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ManageBankAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var bankAccountsWindow = new CashBankWindow();
                bankAccountsWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة إدارة الحسابات المصرفية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الحسابات المصرفية: {ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في فتح نافذة الحسابات المصرفية";
            }
        }

        private void ManageCash_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير إدارة الصندوق قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "إدارة الصندوق - قيد التطوير";
        }

        private void ManageTransactions_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير إدارة الحركات المالية قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "إدارة الحركات المالية - قيد التطوير";
        }

        private void CashDeposit_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير الإيداع النقدي قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "الإيداع النقدي - قيد التطوير";
        }

        private void CashWithdraw_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير السحب النقدي قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "السحب النقدي - قيد التطوير";
        }

        private void BankTransfer_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير التحويل البنكي قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "التحويل البنكي - قيد التطوير";
        }

        private void PayBill_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير دفع الفواتير قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "دفع الفواتير - قيد التطوير";
        }
    }
}
