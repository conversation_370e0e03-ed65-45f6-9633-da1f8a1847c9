"""
تشغيل خادم التطبيق
Run Application Server
"""

import sys
import os
import uvicorn

# إضافة مسار Backend إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_server():
    """تشغيل خادم FastAPI"""
    print("🚀 بدء تشغيل خادم نظام المحاسبة المالية...")
    print("🚀 Starting Financial Accounting System server...")
    
    try:
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    run_server()
