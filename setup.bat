@echo off
echo ========================================
echo    إعداد نظام المحاسبة المالية المتكامل
echo    Setup Integrated Financial Accounting System
echo ========================================
echo.

echo 🔧 بدء إعداد النظام...
echo 🔧 Starting system setup...
echo.

echo 📦 تثبيت متطلبات Python...
echo 📦 Installing Python requirements...
cd Backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات Python
    echo ❌ Failed to install Python requirements
    pause
    exit /b 1
)

echo 📋 إعداد قاعدة البيانات...
echo 📋 Setting up database...
python setup_database.py
if %errorlevel% neq 0 (
    echo ❌ فشل في إعداد قاعدة البيانات
    echo ❌ Failed to setup database
    pause
    exit /b 1
)
cd ..

echo 🔨 بناء Frontend...
echo 🔨 Building Frontend...
cd Frontend\AccountingApp.WPF
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة حزم .NET
    echo ❌ Failed to restore .NET packages
    pause
    exit /b 1
)

dotnet build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء Frontend
    echo ❌ Failed to build Frontend
    pause
    exit /b 1
)
cd ..\..

echo.
echo ✅ تم إعداد النظام بنجاح!
echo ✅ System setup completed successfully!
echo.
echo 📝 معلومات تسجيل الدخول الافتراضية:
echo 📝 Default login credentials:
echo    اسم المستخدم / Username: admin
echo    كلمة المرور / Password: admin123
echo.
echo 🚀 لتشغيل النظام، استخدم: run_app.bat
echo 🚀 To run the system, use: run_app.bat
echo.
pause
