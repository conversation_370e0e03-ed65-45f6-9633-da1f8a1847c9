using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace AccountingApp.WPF.Helpers
{
    /// <summary>
    /// محولات الواجهة
    /// UI Converters
    /// </summary>
    public static class Converters
    {
        /// <summary>
        /// محول Boolean إلى Visibility
        /// Boolean to Visibility Converter
        /// </summary>
        public static readonly IValueConverter BooleanToVisibilityConverter = new BooleanToVisibilityConverterImpl();

        /// <summary>
        /// محول String إلى Visibility
        /// String to Visibility Converter
        /// </summary>
        public static readonly IValueConverter StringToVisibilityConverter = new StringToVisibilityConverterImpl();

        /// <summary>
        /// محول Boolean المعكوس
        /// Inverted Boolean Converter
        /// </summary>
        public static readonly IValueConverter InvertedBooleanConverter = new InvertedBooleanConverterImpl();

        /// <summary>
        /// محول Null إلى Boolean
        /// Null to Boolean Converter
        /// </summary>
        public static readonly IValueConverter NullToBooleanConverter = new NullToBooleanConverterImpl();
    }

    /// <summary>
    /// تطبيق محول Boolean إلى Visibility
    /// Boolean to Visibility Converter Implementation
    /// </summary>
    public class BooleanToVisibilityConverterImpl : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// تطبيق محول String إلى Visibility
    /// String to Visibility Converter Implementation
    /// </summary>
    public class StringToVisibilityConverterImpl : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return string.IsNullOrEmpty(stringValue) ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// تطبيق محول Boolean المعكوس
    /// Inverted Boolean Converter Implementation
    /// </summary>
    public class InvertedBooleanConverterImpl : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }

    /// <summary>
    /// تطبيق محول Null إلى Boolean
    /// Null to Boolean Converter Implementation
    /// </summary>
    public class NullToBooleanConverterImpl : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول التاريخ إلى نص
    /// Date to String Converter
    /// </summary>
    public class DateToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                return dateTime.ToString("dd/MM/yyyy", new CultureInfo("ar-SA"));
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && DateTime.TryParse(stringValue, out DateTime result))
            {
                return result;
            }
            return DateTime.Now;
        }
    }

    /// <summary>
    /// محول المبلغ إلى نص مع التنسيق
    /// Amount to Formatted String Converter
    /// </summary>
    public class AmountToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal amount)
            {
                return amount.ToString("N2", new CultureInfo("ar-SA")) + " ريال";
            }
            return "0.00 ريال";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                var cleanValue = stringValue.Replace("ريال", "").Replace(",", "").Trim();
                if (decimal.TryParse(cleanValue, out decimal result))
                {
                    return result;
                }
            }
            return 0m;
        }
    }
}
