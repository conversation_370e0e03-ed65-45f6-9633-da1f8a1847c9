using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace AccountingApp.WPF.Views.Accounting
{
    public partial class AddAccountWindow : Window
    {
        private readonly AccountViewModel _parentAccount;
        private readonly bool _isEdit;
        private readonly AccountViewModel _editAccount;

        public AddAccountWindow(AccountViewModel parentAccount = null, bool isEdit = false)
        {
            InitializeComponent();
            _parentAccount = parentAccount;
            _isEdit = isEdit;
            
            if (isEdit && parentAccount != null)
            {
                _editAccount = parentAccount;
                HeaderTextBlock.Text = "تعديل الحساب - Edit Account";
                SaveButton.Content = "تحديث";
                LoadAccountData();
            }
            
            LoadParentAccounts();
            
            // تعيين الحساب الأب إذا تم تمريره
            if (parentAccount != null && !isEdit)
            {
                var parentItem = ParentAccountComboBox.Items.Cast<ParentAccountItem>()
                    .FirstOrDefault(x => x.AccountNumber == parentAccount.AccountNumber);
                if (parentItem != null)
                {
                    ParentAccountComboBox.SelectedItem = parentItem;
                }
            }
        }

        private void LoadAccountData()
        {
            if (_editAccount == null) return;

            AccountNumberTextBox.Text = _editAccount.AccountNumber;
            AccountNameTextBox.Text = _editAccount.Name;
            AccountNameArTextBox.Text = _editAccount.NameAr;
            OpeningBalanceTextBox.Text = _editAccount.Balance.ToString("F2");
            IsParentCheckBox.IsChecked = _editAccount.IsParent;
            IsActiveCheckBox.IsChecked = true; // افتراضي
        }

        private void LoadParentAccounts()
        {
            var parentAccounts = new List<ParentAccountItem>
            {
                new ParentAccountItem { AccountNumber = "", DisplayName = "-- لا يوجد حساب أب --" }
            };

            // إضافة الحسابات الرئيسية كخيارات للحساب الأب
            parentAccounts.AddRange(new[]
            {
                new ParentAccountItem { AccountNumber = "1000", DisplayName = "1000 - الأصول (Assets)" },
                new ParentAccountItem { AccountNumber = "1100", DisplayName = "1100 - الأصول المتداولة (Current Assets)" },
                new ParentAccountItem { AccountNumber = "1200", DisplayName = "1200 - الأصول الثابتة (Fixed Assets)" },
                new ParentAccountItem { AccountNumber = "2000", DisplayName = "2000 - الخصوم (Liabilities)" },
                new ParentAccountItem { AccountNumber = "2100", DisplayName = "2100 - الخصوم المتداولة (Current Liabilities)" },
                new ParentAccountItem { AccountNumber = "3000", DisplayName = "3000 - حقوق الملكية (Equity)" },
                new ParentAccountItem { AccountNumber = "4000", DisplayName = "4000 - الإيرادات (Revenue)" },
                new ParentAccountItem { AccountNumber = "5000", DisplayName = "5000 - المصروفات (Expenses)" }
            });

            ParentAccountComboBox.ItemsSource = parentAccounts;
            ParentAccountComboBox.SelectedIndex = 0;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم حفظ البيانات في قاعدة البيانات
                // في الوقت الحالي سنعرض رسالة نجاح فقط
                
                var accountNumber = AccountNumberTextBox.Text.Trim();
                var accountName = AccountNameTextBox.Text.Trim();
                var accountNameAr = AccountNameArTextBox.Text.Trim();
                var openingBalance = decimal.Parse(OpeningBalanceTextBox.Text);
                var isParent = IsParentCheckBox.IsChecked ?? false;
                var isActive = IsActiveCheckBox.IsChecked ?? true;
                var description = DescriptionTextBox.Text.Trim();

                var selectedParent = ParentAccountComboBox.SelectedItem as ParentAccountItem;
                var parentAccountNumber = selectedParent?.AccountNumber;

                // محاكاة حفظ البيانات
                var message = _isEdit ? "تم تحديث الحساب بنجاح!" : "تم إضافة الحساب بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من رقم الحساب
            if (string.IsNullOrWhiteSpace(AccountNumberTextBox.Text))
            {
                ShowError("يرجى إدخال رقم الحساب");
                AccountNumberTextBox.Focus();
                return false;
            }

            // التحقق من اسم الحساب الإنجليزي
            if (string.IsNullOrWhiteSpace(AccountNameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الحساب بالإنجليزية");
                AccountNameTextBox.Focus();
                return false;
            }

            // التحقق من اسم الحساب العربي
            if (string.IsNullOrWhiteSpace(AccountNameArTextBox.Text))
            {
                ShowError("يرجى إدخال اسم الحساب بالعربية");
                AccountNameArTextBox.Focus();
                return false;
            }

            // التحقق من نوع الحساب
            if (AccountTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع الحساب");
                AccountTypeComboBox.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!decimal.TryParse(OpeningBalanceTextBox.Text, out _))
            {
                ShowError("يرجى إدخال رصيد افتتاحي صحيح");
                OpeningBalanceTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار رقم الحساب (محاكاة)
            if (!_isEdit && IsAccountNumberExists(AccountNumberTextBox.Text.Trim()))
            {
                ShowError("رقم الحساب موجود مسبقاً، يرجى اختيار رقم آخر");
                AccountNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsAccountNumberExists(string accountNumber)
        {
            // محاكاة التحقق من وجود رقم الحساب
            var existingNumbers = new[] { "1101", "1102", "1103", "1201", "1202", "1203", "2101", "2102", "2103", "3001", "3002" };
            return existingNumbers.Contains(accountNumber);
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class ParentAccountItem
    {
        public string AccountNumber { get; set; }
        public string DisplayName { get; set; }
    }
}
