"""
خدمة المصادقة والمستخدمين
Authentication and User Service
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import Optional, List
from datetime import datetime, timedelta
from models.users.user import User
from models.users.role import Role, Permission
from core.security import get_password_hash, verify_password, create_access_token
from core.schemas import UserCreate, UserUpdate
from fastapi import HTTPException, status

class AuthService:
    """خدمة المصادقة والمستخدمين"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        مصادقة المستخدم
        Authenticate user with username and password
        """
        user = self.db.query(User).filter(
            and_(
                User.username == username,
                User.is_active == True
            )
        ).first()
        
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        # تحديث آخر تسجيل دخول
        user.last_login = datetime.utcnow()
        self.db.commit()
        
        return user
    
    def create_access_token_for_user(self, user: User) -> str:
        """
        إنشاء رمز الوصول للمستخدم
        Create access token for user
        """
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id},
            expires_delta=access_token_expires
        )
        return access_token
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """الحصول على المستخدم بالاسم"""
        return self.db.query(User).filter(User.username == username).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """الحصول على المستخدم بالبريد الإلكتروني"""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """الحصول على المستخدم بالمعرف"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def create_user(self, user_data: UserCreate) -> User:
        """
        إنشاء مستخدم جديد
        Create new user
        """
        # التحقق من عدم وجود المستخدم
        if self.get_user_by_username(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        if self.get_user_by_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # إنشاء المستخدم
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            phone=user_data.phone,
            is_active=user_data.is_active
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        return db_user
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """
        تحديث بيانات المستخدم
        Update user data
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return None
        
        # تحديث البيانات
        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        تغيير كلمة المرور
        Change user password
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        # التحقق من كلمة المرور القديمة
        if not verify_password(old_password, user.hashed_password):
            return False
        
        # تحديث كلمة المرور
        user.hashed_password = get_password_hash(new_password)
        self.db.commit()
        
        return True
    
    def deactivate_user(self, user_id: int) -> bool:
        """
        إلغاء تفعيل المستخدم
        Deactivate user
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = False
        self.db.commit()
        
        return True
    
    def activate_user(self, user_id: int) -> bool:
        """
        تفعيل المستخدم
        Activate user
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = True
        self.db.commit()
        
        return True
    
    def get_all_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        الحصول على جميع المستخدمين
        Get all users with pagination
        """
        return self.db.query(User).offset(skip).limit(limit).all()
    
    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        الحصول على المستخدمين النشطين
        Get active users with pagination
        """
        return self.db.query(User).filter(User.is_active == True).offset(skip).limit(limit).all()
    
    def search_users(self, search_term: str, skip: int = 0, limit: int = 100) -> List[User]:
        """
        البحث في المستخدمين
        Search users by name, username, or email
        """
        return self.db.query(User).filter(
            User.username.contains(search_term) |
            User.full_name.contains(search_term) |
            User.email.contains(search_term)
        ).offset(skip).limit(limit).all()
    
    def get_user_count(self) -> int:
        """عدد المستخدمين الإجمالي"""
        return self.db.query(User).count()
    
    def get_active_user_count(self) -> int:
        """عدد المستخدمين النشطين"""
        return self.db.query(User).filter(User.is_active == True).count()
