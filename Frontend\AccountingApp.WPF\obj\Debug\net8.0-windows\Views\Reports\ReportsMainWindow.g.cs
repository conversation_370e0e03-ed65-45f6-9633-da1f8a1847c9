﻿#pragma checksum "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "80EAB1DEB5A3D510B767753A5A0AF6F7E89FDB5D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Reports {
    
    
    /// <summary>
    /// ReportsMainWindow
    /// </summary>
    public partial class ReportsMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 56 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPurchasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetProfitTextBlock;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InventoryValueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/reports/reportsmainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TotalSalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalPurchasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NetProfitTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InventoryValueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            
            #line 102 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DailySalesReport_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 103 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MonthlySalesReport_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 104 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TopCustomersReport_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 113 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TopItemsReport_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 114 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SalesByCategoryReport_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 115 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SalesReturnsReport_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 130 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DailyPurchasesReport_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 131 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MonthlyPurchasesReport_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 132 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TopSuppliersReport_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 141 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PurchasesByCategoryReport_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 142 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SuppliersBalanceReport_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 143 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PurchaseReturnsReport_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 158 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.IncomeStatementReport_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 159 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BalanceSheetReport_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 160 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CashFlowReport_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 169 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ProfitLossReport_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 170 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TaxReport_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 171 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExpensesReport_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 186 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InventoryStatusReport_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 187 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LowStockReport_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 188 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InventoryMovementReport_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 197 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InventoryValuationReport_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 198 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SlowMovingItemsReport_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 199 "..\..\..\..\..\Views\Reports\ReportsMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StockTakeReport_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

