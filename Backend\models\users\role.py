"""
نماذج الأدوار والصلاحيات
Roles and Permissions Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

# جدول ربط المستخدمين بالأدوار (Many-to-Many)
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

# جدول ربط الأدوار بالصلاحيات (Many-to-Many)
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)

class Role(Base):
    """
    نموذج الدور
    Role model for user authorization
    """
    __tablename__ = "roles"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # اسم الدور
    name = Column(String(50), unique=True, nullable=False)
    
    # اسم الدور بالعربية
    name_ar = Column(String(100), nullable=False)
    
    # وصف الدور
    description = Column(Text, nullable=True)
    
    # هل الدور نشط
    is_active = Column(Boolean, default=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', name_ar='{self.name_ar}')>"

class Permission(Base):
    """
    نموذج الصلاحية
    Permission model for system authorization
    """
    __tablename__ = "permissions"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # اسم الصلاحية
    name = Column(String(100), unique=True, nullable=False)
    
    # اسم الصلاحية بالعربية
    name_ar = Column(String(150), nullable=False)
    
    # وصف الصلاحية
    description = Column(Text, nullable=True)
    
    # المجموعة (مثل: accounting, sales, inventory)
    module = Column(String(50), nullable=False)
    
    # نوع العملية (create, read, update, delete)
    action = Column(String(20), nullable=False)
    
    # هل الصلاحية نشطة
    is_active = Column(Boolean, default=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")

    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}', module='{self.module}', action='{self.action}')>"
