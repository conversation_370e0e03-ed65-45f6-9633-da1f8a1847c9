<Window x:Class="AccountingApp.WPF.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام المحاسبة المالية"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="نظام المحاسبة المالية المتكامل"
                          FontSize="20"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="Integrated Financial Accounting System"
                          FontSize="12"
                          Foreground="#E0E0E0"
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Background="White" Margin="30" CornerRadius="10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
            </Border.Effect>

            <StackPanel Margin="30" VerticalAlignment="Center">
                <!-- Title -->
                <TextBlock Text="تسجيل الدخول"
                          FontSize="24"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"
                          Foreground="#333333"/>

                <!-- Username -->
                <TextBlock Text="اسم المستخدم:"
                          FontSize="14"
                          FontWeight="SemiBold"
                          Margin="0,0,0,5"
                          Foreground="#555555"/>
                <TextBox x:Name="UsernameTextBox"
                        Style="{StaticResource ModernTextBox}"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Password -->
                <TextBlock Text="كلمة المرور:"
                          FontSize="14"
                          FontWeight="SemiBold"
                          Margin="0,0,0,5"
                          Foreground="#555555"/>
                <PasswordBox x:Name="PasswordBox"
                            Style="{StaticResource ModernPasswordBox}"
                            Margin="0,0,0,15"/>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني"
                         IsChecked="{Binding RememberMe}"
                         Margin="0,0,0,20"
                         FontSize="12"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,15">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Setter Property="Visibility" Value="Visible"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ErrorMessage}" Value="">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding ErrorMessage}" Value="{x:Null}">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- Login Button -->
                <Button Content="تسجيل الدخول"
                       Style="{StaticResource ModernButton}"
                       Command="{Binding LoginCommand}"
                       IsEnabled="{Binding IsLoginEnabled}"
                       Margin="0,0,0,10"/>

                <!-- Cancel Button -->
                <Button Content="إلغاء"
                       Style="{StaticResource ModernButton}"
                       Background="#6C757D"
                       Command="{Binding CancelCommand}"/>

                <!-- Loading Indicator -->
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           Margin="0,15,0,0">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <TextBlock Text="●" FontSize="20" Foreground="#007ACC">
                        <TextBlock.RenderTransform>
                            <RotateTransform/>
                        </TextBlock.RenderTransform>
                        <TextBlock.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                       From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </TextBlock.Triggers>
                    </TextBlock>
                    <TextBlock Text="{Binding BusyMessage}"
                              FontSize="12"
                              Foreground="#666666"
                              Margin="10,0,0,0"
                              VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
