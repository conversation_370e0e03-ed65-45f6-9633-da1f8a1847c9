<Window x:Class="AccountingApp.WPF.Views.Finance.BankTransferWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="التحويلات البنكية - Bank Transfers"
        Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#6F42C1" Padding="15">
            <TextBlock Text="التحويلات البنكية - Bank Transfers"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <StackPanel Grid.Row="1" Margin="20" VerticalAlignment="Center">
            <!-- Transfer Type -->
            <TextBlock Text="نوع التحويل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="TransferTypeComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15">
                <ComboBoxItem Content="من البنك إلى الصندوق" Tag="BankToCash" IsSelected="True"/>
                <ComboBoxItem Content="من الصندوق إلى البنك" Tag="CashToBank"/>
                <ComboBoxItem Content="من بنك إلى بنك" Tag="BankToBank"/>
            </ComboBox>

            <!-- From Account -->
            <TextBlock Text="من الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="FromAccountComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15">
                <ComboBoxItem Content="BANK001 - البنك الأهلي - الحساب الجاري"/>
                <ComboBoxItem Content="BANK002 - بنك الراجحي - حساب التوفير"/>
                <ComboBoxItem Content="CASH001 - الصندوق الرئيسي"/>
            </ComboBox>

            <!-- To Account -->
            <TextBlock Text="إلى الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="ToAccountComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15">
                <ComboBoxItem Content="CASH001 - الصندوق الرئيسي"/>
                <ComboBoxItem Content="BANK001 - البنك الأهلي - الحساب الجاري"/>
                <ComboBoxItem Content="BANK002 - بنك الراجحي - حساب التوفير"/>
            </ComboBox>

            <!-- Amount and Fees -->
            <Grid Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="المبلغ *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="AmountTextBox"
                            Text="0.00"
                            Padding="8"
                            FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="رسوم التحويل" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="FeesTextBox"
                            Text="0.00"
                            Padding="8"
                            FontSize="14"/>
                </StackPanel>
            </Grid>

            <!-- Description -->
            <TextBlock Text="الوصف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="DescriptionTextBox"
                    Padding="8"
                    FontSize="14"
                    Height="60"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    Margin="0,0,0,15"/>

            <!-- Reference and Date -->
            <Grid Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="رقم المرجع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ReferenceTextBox"
                            Padding="8"
                            FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="تاريخ التحويل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="TransferDatePicker"
                               Padding="8"
                               FontSize="14"
                               SelectedDate="{x:Static sys:DateTime.Today}"/>
                </StackPanel>
            </Grid>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorTextBlock"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      Margin="0,0,0,10"
                      Visibility="Collapsed"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تنفيذ التحويل"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="ExecuteButton_Click"/>

                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
