<Window x:Class="AccountingApp.WPF.Views.Reports.CustomersReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير أفضل العملاء - Top Customers Report"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#28A745" Padding="15">
            <Grid>
                <TextBlock Text="تقرير أفضل العملاء - Top Customers Report"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Report Data -->
        <Border Grid.Row="1" Background="White" Padding="10">
            <DataGrid x:Name="CustomersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الترتيب" Binding="{Binding Rank}" Width="80"/>
                    <DataGridTextColumn Header="اسم العميل" Binding="{Binding CustomerName}" Width="250"/>
                    <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120"/>
                    <DataGridTextColumn Header="إجمالي المشتريات" Binding="{Binding TotalPurchases, StringFormat=F2}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Green"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="متوسط الفاتورة" Binding="{Binding AverageInvoice, StringFormat=F2}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="Blue"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="آخر شراء" Binding="{Binding LastPurchaseDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Actions -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="طباعة التقرير" Background="#6C757D" Foreground="White" Padding="20,10" Margin="10" Click="PrintReport_Click"/>
                <Button Content="تصدير إلى Excel" Background="#198754" Foreground="White" Padding="20,10" Margin="10" Click="ExportToExcel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
