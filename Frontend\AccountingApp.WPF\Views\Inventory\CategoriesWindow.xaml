<Window x:Class="AccountingApp.WPF.Views.Inventory.CategoriesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة فئات الأصناف - Categories Management"
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#6F42C1" Padding="15">
            <Grid>
                <TextBlock Text="إدارة فئات الأصناف - Categories Management"
                          FontSize="16"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="فئة جديدة"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddCategory_Click"/>
                
                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditCategory_Click"/>
                
                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteCategory_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="200"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>

        <!-- Categories DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="CategoriesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="CategoriesDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الفئة" Binding="{Binding CategoryCode}" Width="100"/>
                    <DataGridTextColumn Header="اسم الفئة" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="الاسم بالعربية" Binding="{Binding NameAr}" Width="200"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="250"/>
                    <DataGridTextColumn Header="عدد الأصناف" Binding="{Binding ItemCount}" Width="100"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر فئة للعرض أو التعديل" 
                          HorizontalAlignment="Right"/>
                <TextBlock x:Name="CategoryCountTextBlock"
                          Text="عدد الفئات: 0"
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
