using System.Windows;
using System.Windows.Input;
using AccountingApp.WPF.Views.Accounting;

namespace AccountingApp.WPF.Views
{
    public partial class AccountingMenuWindow : Window
    {
        public AccountingMenuWindow()
        {
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ChartOfAccounts_Click(object sender, MouseButtonEventArgs e)
        {
            var chartWindow = new ChartOfAccountsWindow();
            chartWindow.ShowDialog();
        }

        private void JournalEntries_Click(object sender, MouseButtonEventArgs e)
        {
            var journalWindow = new JournalEntriesWindow();
            journalWindow.ShowDialog();
        }

        private void TrialBalance_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزان المراجعة قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void GeneralLedger_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("سيتم تطوير دفتر الأستاذ العام قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
