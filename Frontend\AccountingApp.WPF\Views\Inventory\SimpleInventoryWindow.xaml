<Window x:Class="AccountingApp.WPF.Views.Inventory.SimpleInventoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المخازن - Inventory Management"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="إدارة المخازن والأصناف - Inventory Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                
                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Menu Buttons -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20">
            <StackPanel>
                <TextBlock Text="اختر الوحدة التي تريد إدارتها:" 
                          FontSize="14" 
                          FontWeight="SemiBold" 
                          Margin="0,0,0,15"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="إدارة الأصناف"
                           Background="#28A745"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageItems_Click"/>
                    
                    <Button Content="إدارة الفئات"
                           Background="#6F42C1"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageCategories_Click"/>
                    
                    <Button Content="إدارة الوحدات"
                           Background="#E83E8C"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageUnits_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Quick Summary -->
        <Border Grid.Row="2" Background="White" Padding="20">
            <StackPanel>
                <TextBlock Text="ملخص سريع للمخزون:" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Margin="0,0,0,20"
                          Foreground="#007ACC"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Total Items -->
                    <Border Grid.Column="0" Background="#E3F2FD" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي الأصناف" 
                                      FontSize="14" 
                                      FontWeight="SemiBold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#1976D2"/>
                            <TextBlock x:Name="TotalItemsTextBlock"
                                      Text="25 صنف" 
                                      FontSize="24" 
                                      FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#1976D2"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Low Stock Items -->
                    <Border Grid.Column="1" Background="#FFEBEE" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="أصناف منخفضة المخزون" 
                                      FontSize="14" 
                                      FontWeight="SemiBold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#D32F2F"/>
                            <TextBlock x:Name="LowStockItemsTextBlock"
                                      Text="3 أصناف" 
                                      FontSize="24" 
                                      FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#D32F2F"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Value -->
                    <Border Grid.Column="2" Background="#E8F5E8" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي قيمة المخزون" 
                                      FontSize="14" 
                                      FontWeight="SemiBold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#388E3C"/>
                            <TextBlock x:Name="TotalValueTextBlock"
                                      Text="125,500 ريال" 
                                      FontSize="24" 
                                      FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="#388E3C"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>
                
                <!-- Recent Items -->
                <TextBlock Text="آخر الأصناف المضافة:" 
                          FontSize="14" 
                          FontWeight="SemiBold" 
                          Margin="0,30,0,10"/>
                
                <ListBox x:Name="RecentItemsListBox" 
                        Height="150" 
                        Background="#F8F9FA"
                        BorderBrush="#DEE2E6"
                        BorderThickness="1">
                    <ListBoxItem Content="لابتوب ديل انسبايرون - ITM001"/>
                    <ListBoxItem Content="كرسي مكتب - ITM002"/>
                    <ListBoxItem Content="حزمة ورق A4 - ITM003"/>
                    <ListBoxItem Content="فأرة لاسلكية - ITM004"/>
                    <ListBoxItem Content="الدعم الفني - SRV001"/>
                </ListBox>
            </StackPanel>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="جاهز - اختر وحدة للبدء" 
                          HorizontalAlignment="Right"/>
                <TextBlock Text="نظام إدارة المخازن" 
                          Foreground="Green" 
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
