<Window x:Class="AccountingApp.WPF.Views.Sales.DiscountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تطبيق خصم - Apply Discount"
        Height="300" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FFC107" Padding="15">
            <TextBlock Text="تطبيق خصم - Apply Discount"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="Black"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <StackPanel Grid.Row="1" Margin="20" VerticalAlignment="Center">
            <!-- Discount Type -->
            <TextBlock Text="نوع الخصم" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <ComboBox x:Name="DiscountTypeComboBox"
                     Padding="8"
                     FontSize="14"
                     Margin="0,0,0,15"
                     SelectionChanged="DiscountTypeComboBox_SelectionChanged">
                <ComboBoxItem Content="نسبة مئوية %" Tag="Percentage" IsSelected="True"/>
                <ComboBoxItem Content="مبلغ ثابت" Tag="Amount"/>
            </ComboBox>

            <!-- Discount Value -->
            <TextBlock x:Name="DiscountValueLabel" Text="نسبة الخصم %" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox x:Name="DiscountValueTextBox"
                    Text="0"
                    Padding="8"
                    FontSize="14"
                    Margin="0,0,0,15"
                    TextChanged="DiscountValueTextBox_TextChanged"/>

            <!-- Preview -->
            <Border Background="#E9ECEF" Padding="10" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="معاينة الخصم:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <Grid>
                        <TextBlock x:Name="OriginalAmountLabel" Text="المبلغ الأصلي: 0.00" HorizontalAlignment="Right"/>
                    </Grid>
                    <Grid>
                        <TextBlock x:Name="DiscountAmountLabel" Text="قيمة الخصم: 0.00" HorizontalAlignment="Right" Foreground="Red"/>
                    </Grid>
                    <Grid>
                        <TextBlock x:Name="FinalAmountLabel" Text="المبلغ النهائي: 0.00" HorizontalAlignment="Right" Foreground="Green" FontWeight="Bold"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorTextBlock"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      Margin="0,0,0,10"
                      Visibility="Collapsed"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تطبيق"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="ApplyButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
