"""
نموذج دليل الحسابات
Chart of Accounts Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class AccountType(Base):
    """
    نموذج أنواع الحسابات
    Account Types Model (Assets, Liabilities, Equity, Revenue, Expenses)
    """
    __tablename__ = "account_types"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # اسم نوع الحساب
    name = Column(String(50), unique=True, nullable=False)
    
    # اسم نوع الحساب بالعربية
    name_ar = Column(String(100), nullable=False)
    
    # رمز نوع الحساب
    code = Column(String(10), unique=True, nullable=False)
    
    # طبيعة الحساب (debit/credit)
    nature = Column(String(10), nullable=False)  # 'debit' or 'credit'
    
    # ترتيب العرض
    display_order = Column(Integer, default=0)
    
    # هل النوع نشط
    is_active = Column(Boolean, default=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    accounts = relationship("Account", back_populates="account_type")

    def __repr__(self):
        return f"<AccountType(id={self.id}, name='{self.name}', code='{self.code}')>"

class Account(Base):
    """
    نموذج الحسابات
    Accounts Model - Chart of Accounts
    """
    __tablename__ = "accounts"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم الحساب
    account_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # اسم الحساب
    name = Column(String(100), nullable=False)
    
    # اسم الحساب بالعربية
    name_ar = Column(String(150), nullable=False)
    
    # نوع الحساب
    account_type_id = Column(Integer, ForeignKey('account_types.id'), nullable=False)
    
    # الحساب الأب (للحسابات الفرعية)
    parent_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    
    # مستوى الحساب في الشجرة
    level = Column(Integer, default=1)
    
    # هل الحساب رئيسي أم فرعي
    is_parent = Column(Boolean, default=False)
    
    # هل يمكن الترحيل إلى هذا الحساب
    is_postable = Column(Boolean, default=True)
    
    # الرصيد الافتتاحي
    opening_balance = Column(Numeric(15, 2), default=0.00)
    
    # الرصيد الحالي
    current_balance = Column(Numeric(15, 2), default=0.00)
    
    # وصف الحساب
    description = Column(Text, nullable=True)
    
    # هل الحساب نشط
    is_active = Column(Boolean, default=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    account_type = relationship("AccountType", back_populates="accounts")
    parent = relationship("Account", remote_side=[id], backref="children")
    
    def __repr__(self):
        return f"<Account(id={self.id}, number='{self.account_number}', name='{self.name}')>"

    @property
    def full_name(self):
        """الحصول على الاسم الكامل للحساب مع الحساب الأب"""
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name

    @property
    def full_name_ar(self):
        """الحصول على الاسم الكامل للحساب بالعربية مع الحساب الأب"""
        if self.parent:
            return f"{self.parent.name_ar} - {self.name_ar}"
        return self.name_ar
