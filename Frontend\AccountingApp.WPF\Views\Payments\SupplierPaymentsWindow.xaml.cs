using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;

namespace AccountingApp.WPF.Views.Payments
{
    public partial class SupplierPaymentsWindow : Window
    {
        private ObservableCollection<SupplierPaymentViewModel> _payments;
        private SupplierPaymentViewModel _selectedPayment;

        public SupplierPaymentsWindow()
        {
            // محاكاة نافذة مدفوعات الموردين
            MessageBox.Show("سيتم تطوير نافذة مدفوعات الموردين قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            LoadSampleData();
        }

        private void SetDefaultDates()
        {
            // محاكاة تعيين التواريخ الافتراضية
        }

        private void LoadSampleData()
        {
            _payments = new ObservableCollection<SupplierPaymentViewModel>
            {
                new SupplierPaymentViewModel
                {
                    PaymentNumber = "PAY-2024-001",
                    PaymentDate = DateTime.Today,
                    SupplierName = "شركة التوريدات المتقدمة",
                    InvoiceNumber = "PUR-2024-001",
                    Amount = 9200,
                    PaymentMethod = "شيك",
                    ReferenceNumber = "CHK-123456",
                    Status = "Completed",
                    Notes = "دفعة كاملة"
                },
                new SupplierPaymentViewModel
                {
                    PaymentNumber = "PAY-2024-002",
                    PaymentDate = DateTime.Today.AddDays(-1),
                    SupplierName = "مؤسسة الإمدادات الصناعية",
                    InvoiceNumber = "PUR-2024-002",
                    Amount = 10000,
                    PaymentMethod = "تحويل بنكي",
                    ReferenceNumber = "TRF-789012",
                    Status = "Completed",
                    Notes = "دفعة جزئية"
                }
            };

            // محاكاة ربط البيانات
            MessageBox.Show("تم تحميل مدفوعات الموردين", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    public class SupplierPaymentViewModel
    {
        public string PaymentNumber { get; set; } = "";
        public DateTime PaymentDate { get; set; }
        public string SupplierName { get; set; } = "";
        public string InvoiceNumber { get; set; } = "";
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = "";
        public string ReferenceNumber { get; set; } = "";
        public string Status { get; set; } = "";
        public string Notes { get; set; } = "";

        public string StatusText => Status switch
        {
            "Completed" => "مكتمل",
            "Pending" => "معلق",
            "Cancelled" => "ملغي",
            _ => "غير محدد"
        };

        public string StatusColor => Status switch
        {
            "Completed" => "Green",
            "Pending" => "Orange",
            "Cancelled" => "Red",
            _ => "Black"
        };
    }
}
