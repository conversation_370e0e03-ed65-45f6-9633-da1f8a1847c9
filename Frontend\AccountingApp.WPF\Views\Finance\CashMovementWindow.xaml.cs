using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Finance
{
    public partial class CashMovementWindow : Window
    {
        public CashMovementWindow()
        {
            InitializeComponent();
        }

        private void ExecuteButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                // هنا يتم تنفيذ الحركة النقدية
                var movementType = (MovementTypeComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString();
                var amount = decimal.Parse(AmountTextBox.Text);
                var description = DescriptionTextBox.Text.Trim();
                var reference = ReferenceTextBox.Text.Trim();

                // محاكاة تنفيذ الحركة
                var movementText = movementType == "Deposit" ? "إيداع" : "سحب";
                var message = $"تم تنفيذ {movementText} بمبلغ {amount:C} بنجاح!";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تنفيذ الحركة: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // التحقق من نوع الحركة
            if (MovementTypeComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار نوع الحركة");
                MovementTypeComboBox.Focus();
                return false;
            }

            // التحقق من الحساب النقدي
            if (CashAccountComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الحساب النقدي");
                CashAccountComboBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            {
                ShowError("يرجى إدخال مبلغ صحيح أكبر من الصفر");
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                ShowError("يرجى إدخال وصف الحركة");
                DescriptionTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
