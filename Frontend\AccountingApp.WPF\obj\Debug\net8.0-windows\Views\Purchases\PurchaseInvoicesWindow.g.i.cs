﻿#pragma checksum "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8C8E6588EB44E142760C71DA91295AC63C940BAC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Purchases {
    
    
    /// <summary>
    /// PurchaseInvoicesWindow
    /// </summary>
    public partial class PurchaseInvoicesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 79 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPurchasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTaxTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/purchases/purchaseinvoiceswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 32 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 44 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewInvoice_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 51 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewEditInvoice_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 58 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintInvoice_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 65 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoice_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 74 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageSuppliers_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 83 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 90 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 95 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 115 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 130 "..\..\..\..\..\Views\Purchases\PurchaseInvoicesWindow.xaml"
            this.InvoicesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InvoicesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.InvoiceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TotalPurchasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TotalTaxTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

