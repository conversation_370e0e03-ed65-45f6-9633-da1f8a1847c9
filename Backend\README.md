# Backend - نظام المحاسبة المالية
## Financial Accounting System Backend

Backend API للنظام المحاسبي المتكامل مبني بـ Python FastAPI

### المتطلبات
- Python 3.8+
- FastAPI
- SQLAlchemy
- PostgreSQL أو SQLite

### التثبيت والإعداد

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. إعداد قاعدة البيانات
```bash
python setup_database.py
```

#### 3. تشغيل الخادم
```bash
python run_server.py
```

أو باستخدام uvicorn مباشرة:
```bash
uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### الوصول للنظام

- **API Documentation**: http://127.0.0.1:8000/docs
- **Alternative Docs**: http://127.0.0.1:8000/redoc
- **Health Check**: http://127.0.0.1:8000/health

### معلومات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### هيكل المشروع

```
Backend/
├── main.py                 # التطبيق الرئيسي
├── setup_database.py       # إعداد قاعدة البيانات
├── run_server.py          # تشغيل الخادم
├── requirements.txt       # المتطلبات
├── .env                   # متغيرات البيئة
├── core/                  # الوحدات الأساسية
│   ├── security.py        # الأمان والمصادقة
│   └── schemas.py         # نماذج البيانات
├── models/                # نماذج قاعدة البيانات
│   ├── users/            # نماذج المستخدمين
│   ├── accounting/       # نماذج المحاسبة
│   ├── sales/            # نماذج المبيعات
│   ├── purchases/        # نماذج المشتريات
│   ├── inventory/        # نماذج المخازن
│   └── financial/        # نماذج الصندوق والبنوك
├── services/             # خدمات الأعمال
│   ├── auth_service.py   # خدمة المصادقة
│   └── accounting_service.py # خدمة المحاسبة
├── api/                  # نقاط النهاية
│   ├── auth.py          # API المصادقة
│   └── accounting.py    # API المحاسبة
├── database/            # إدارة قاعدة البيانات
│   ├── config.py        # إعدادات قاعدة البيانات
│   └── init_data.py     # البيانات الأساسية
├── reports/             # تقارير
├── utils/               # أدوات مساعدة
└── tests/               # الاختبارات
```

### الوحدات المتاحة

#### 1. المصادقة والمستخدمين
- تسجيل الدخول
- إدارة المستخدمين
- نظام الصلاحيات

#### 2. المحاسبة العامة
- دليل الحسابات
- أنواع الحسابات
- القيود اليومية
- أرصدة الحسابات

#### 3. المبيعات والعملاء (قيد التطوير)
- إدارة العملاء
- فواتير المبيعات
- مرتجعات المبيعات

#### 4. المشتريات والموردين (قيد التطوير)
- إدارة الموردين
- فواتير المشتريات
- مرتجعات المشتريات

#### 5. المخازن والأصناف (قيد التطوير)
- إدارة الأصناف
- إدارة المخازن
- حركات المخزون

#### 6. الصندوق والبنوك (قيد التطوير)
- إدارة الحسابات النقدية
- سندات القبض والصرف
- التحويلات

### API Endpoints

#### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - معلومات المستخدم الحالي
- `POST /api/auth/users` - إنشاء مستخدم جديد
- `GET /api/auth/users` - قائمة المستخدمين

#### المحاسبة
- `GET /api/accounting/account-types` - أنواع الحسابات
- `GET /api/accounting/accounts` - الحسابات
- `POST /api/accounting/accounts` - إنشاء حساب جديد
- `GET /api/accounting/accounts/{id}/balance` - رصيد الحساب
- `POST /api/accounting/journal-entries` - إنشاء قيد يومي

### متغيرات البيئة

قم بإنشاء ملف `.env` أو تعديل الموجود:

```env
DATABASE_URL=sqlite:///./accounting_app.db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### التطوير

#### إضافة وحدة جديدة
1. إنشاء النماذج في `models/`
2. إنشاء الخدمات في `services/`
3. إنشاء API في `api/`
4. تسجيل المسارات في `main.py`

#### تشغيل الاختبارات
```bash
pytest tests/
```

### الأمان
- تشفير كلمات المرور باستخدام bcrypt
- JWT tokens للمصادقة
- نظام صلاحيات متقدم
- حماية CORS

### قاعدة البيانات
- SQLAlchemy ORM
- دعم SQLite و PostgreSQL
- Migration system مع Alembic
- البيانات الأساسية تلقائياً

### الدعم
للمساعدة أو الاستفسارات، يرجى مراجعة الوثائق أو التواصل مع فريق التطوير.
