﻿<Application x:Class="AccountingApp.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:AccountingApp.WPF">
    <Application.Resources>
        <!-- إعدادات الخطوط العربية -->
        <Style TargetType="{x:Type Window}">
            <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style TargetType="{x:Type TextBlock}">
            <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
        </Style>

        <Style TargetType="{x:Type TextBox}">
            <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
        </Style>

        <Style TargetType="{x:<PERSON> Button}">
            <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
        </Style>
    </Application.Resources>
</Application>
