<Window x:Class="AccountingApp.WPF.Views.Sales.CustomersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة العملاء - Customer Management"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <Grid>
                <TextBlock Text="إدارة العملاء - Customer Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>

                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="عميل جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddCustomer_Click"/>

                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditCustomer_Click"/>

                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteCustomer_Click"/>

                <Separator Margin="10,0"/>

                <Button Content="فواتير المبيعات"
                       Background="#6F42C1"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="SalesInvoices_Click"/>

                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="5"/>
                <TextBox x:Name="SearchTextBox"
                        Width="200"
                        Padding="5"
                        Margin="5"
                        TextChanged="SearchTextBox_TextChanged"/>

                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>

                <Separator Margin="10,0"/>

                <Button Content="تصدير Excel"
                       Background="#17A2B8"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="ExportButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Customers DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="CustomersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="CustomersDataGrid_SelectionChanged">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم العميل" Binding="{Binding CustomerNumber}" Width="100"/>
                    <DataGridTextColumn Header="اسم العميل" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="الاسم بالعربية" Binding="{Binding NameAr}" Width="200"/>
                    <DataGridTextColumn Header="نوع العميل" Binding="{Binding CustomerTypeText}" Width="100"/>
                    <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                    <DataGridTextColumn Header="المدينة" Binding="{Binding City}" Width="100"/>
                    <DataGridTextColumn Header="الرصيد الحالي" Binding="{Binding CurrentBalance, StringFormat=C}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding BalanceColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock"
                          Text="جاهز - اختر عميل للعرض أو التعديل"
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="CustomerCountTextBlock" Text="عدد العملاء: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalBalanceTextBlock" Text="إجمالي الأرصدة: 0.00" Foreground="Green" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
