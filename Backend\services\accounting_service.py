"""
خدمة المحاسبة
Accounting Service
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List
from decimal import Decimal
from datetime import date, datetime

from models.accounting.chart_of_accounts import AccountType, Account
from models.accounting.journal_entry import JournalEntry, JournalEntryDetail
from core.schemas import AccountCreate, AccountUpdate
from fastapi import HTTPException, status

class AccountingService:
    """خدمة المحاسبة العامة"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # ===== إدارة أنواع الحسابات =====
    
    def get_account_types(self) -> List[AccountType]:
        """الحصول على جميع أنواع الحسابات"""
        return self.db.query(AccountType).filter(AccountType.is_active == True).order_by(AccountType.display_order).all()
    
    def get_account_type_by_id(self, type_id: int) -> Optional[AccountType]:
        """الحصول على نوع حساب بالمعرف"""
        return self.db.query(AccountType).filter(AccountType.id == type_id).first()
    
    # ===== إدارة الحسابات =====
    
    def get_accounts(self, account_type_id: Optional[int] = None, parent_id: Optional[int] = None) -> List[Account]:
        """الحصول على الحسابات مع إمكانية التصفية"""
        query = self.db.query(Account).filter(Account.is_active == True)
        
        if account_type_id:
            query = query.filter(Account.account_type_id == account_type_id)
        
        if parent_id is not None:
            query = query.filter(Account.parent_id == parent_id)
        
        return query.order_by(Account.account_number).all()
    
    def get_account_by_id(self, account_id: int) -> Optional[Account]:
        """الحصول على حساب بالمعرف"""
        return self.db.query(Account).filter(Account.id == account_id).first()
    
    def get_account_by_number(self, account_number: str) -> Optional[Account]:
        """الحصول على حساب برقم الحساب"""
        return self.db.query(Account).filter(Account.account_number == account_number).first()
    
    def create_account(self, account_data: AccountCreate) -> Account:
        """إنشاء حساب جديد"""
        # التحقق من عدم وجود رقم الحساب
        if self.get_account_by_number(account_data.account_number):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Account number already exists"
            )
        
        # التحقق من وجود نوع الحساب
        account_type = self.get_account_type_by_id(account_data.account_type_id)
        if not account_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid account type"
            )
        
        # التحقق من الحساب الأب إذا كان محدد
        if account_data.parent_id:
            parent_account = self.get_account_by_id(account_data.parent_id)
            if not parent_account:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid parent account"
                )
            # تحديد المستوى
            account_data.level = parent_account.level + 1
        
        # إنشاء الحساب
        db_account = Account(**account_data.dict())
        db_account.current_balance = account_data.opening_balance
        
        self.db.add(db_account)
        self.db.commit()
        self.db.refresh(db_account)
        
        return db_account
    
    def update_account(self, account_id: int, account_data: AccountUpdate) -> Optional[Account]:
        """تحديث بيانات الحساب"""
        account = self.get_account_by_id(account_id)
        if not account:
            return None
        
        # تحديث البيانات
        update_data = account_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(account, field, value)
        
        self.db.commit()
        self.db.refresh(account)
        
        return account
    
    def get_account_balance(self, account_id: int, as_of_date: Optional[date] = None) -> Decimal:
        """الحصول على رصيد الحساب في تاريخ معين"""
        account = self.get_account_by_id(account_id)
        if not account:
            return Decimal('0.00')
        
        if not as_of_date:
            return account.current_balance
        
        # حساب الرصيد حتى تاريخ معين
        query = self.db.query(
            func.sum(JournalEntryDetail.debit_amount - JournalEntryDetail.credit_amount)
        ).join(JournalEntry).filter(
            and_(
                JournalEntryDetail.account_id == account_id,
                JournalEntry.entry_date <= as_of_date,
                JournalEntry.status == 'posted'
            )
        )
        
        balance_change = query.scalar() or Decimal('0.00')
        return account.opening_balance + balance_change
    
    def search_accounts(self, search_term: str) -> List[Account]:
        """البحث في الحسابات"""
        return self.db.query(Account).filter(
            and_(
                Account.is_active == True,
                or_(
                    Account.account_number.contains(search_term),
                    Account.name.contains(search_term),
                    Account.name_ar.contains(search_term)
                )
            )
        ).order_by(Account.account_number).all()
    
    def get_postable_accounts(self) -> List[Account]:
        """الحصول على الحسابات القابلة للترحيل"""
        return self.db.query(Account).filter(
            and_(
                Account.is_active == True,
                Account.is_postable == True
            )
        ).order_by(Account.account_number).all()
    
    # ===== إدارة القيود اليومية =====
    
    def generate_entry_number(self) -> str:
        """توليد رقم قيد جديد"""
        # الحصول على آخر رقم قيد
        last_entry = self.db.query(JournalEntry).order_by(JournalEntry.id.desc()).first()
        
        if last_entry:
            # استخراج الرقم من آخر قيد وزيادته
            try:
                last_number = int(last_entry.entry_number.replace('JE', ''))
                new_number = last_number + 1
            except:
                new_number = 1
        else:
            new_number = 1
        
        return f"JE{new_number:06d}"
    
    def create_journal_entry(
        self,
        entry_date: date,
        description: str,
        details: List[dict],
        reference: Optional[str] = None,
        entry_type: str = 'manual',
        created_by: int = 1
    ) -> JournalEntry:
        """إنشاء قيد يومي جديد"""
        
        # التحقق من توازن القيد
        total_debit = sum(Decimal(str(detail.get('debit_amount', 0))) for detail in details)
        total_credit = sum(Decimal(str(detail.get('credit_amount', 0))) for detail in details)
        
        if abs(total_debit - total_credit) > Decimal('0.01'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Journal entry is not balanced"
            )
        
        # إنشاء القيد الرئيسي
        entry_number = self.generate_entry_number()
        journal_entry = JournalEntry(
            entry_number=entry_number,
            entry_date=entry_date,
            description=description,
            reference=reference,
            entry_type=entry_type,
            total_amount=total_debit,
            created_by=created_by
        )
        
        self.db.add(journal_entry)
        self.db.flush()  # للحصول على ID
        
        # إضافة تفاصيل القيد
        for i, detail in enumerate(details):
            # التحقق من وجود الحساب
            account = self.get_account_by_id(detail['account_id'])
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid account ID: {detail['account_id']}"
                )
            
            if not account.is_postable:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Account {account.account_number} is not postable"
                )
            
            entry_detail = JournalEntryDetail(
                journal_entry_id=journal_entry.id,
                account_id=detail['account_id'],
                description=detail.get('description', ''),
                debit_amount=Decimal(str(detail.get('debit_amount', 0))),
                credit_amount=Decimal(str(detail.get('credit_amount', 0))),
                line_order=i + 1
            )
            
            self.db.add(entry_detail)
        
        self.db.commit()
        self.db.refresh(journal_entry)
        
        return journal_entry
    
    def post_journal_entry(self, entry_id: int, posted_by: int) -> bool:
        """ترحيل القيد اليومي"""
        entry = self.db.query(JournalEntry).filter(JournalEntry.id == entry_id).first()
        if not entry:
            return False
        
        if entry.status != 'draft':
            return False
        
        # تحديث أرصدة الحسابات
        for detail in entry.details:
            account = detail.account
            if detail.debit_amount > 0:
                account.current_balance += detail.debit_amount
            else:
                account.current_balance -= detail.credit_amount
        
        # تحديث حالة القيد
        entry.status = 'posted'
        entry.posted_date = datetime.utcnow()
        entry.posted_by = posted_by
        
        self.db.commit()
        return True
    
    def get_journal_entries(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[JournalEntry]:
        """الحصول على القيود اليومية مع التصفية"""
        query = self.db.query(JournalEntry)
        
        if start_date:
            query = query.filter(JournalEntry.entry_date >= start_date)
        
        if end_date:
            query = query.filter(JournalEntry.entry_date <= end_date)
        
        if status:
            query = query.filter(JournalEntry.status == status)
        
        return query.order_by(JournalEntry.entry_date.desc(), JournalEntry.entry_number.desc()).offset(skip).limit(limit).all()
