"""
نماذج البيانات (Pydantic Schemas)
Data Models for API validation
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal

# نماذج المصادقة
class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# نماذج المستخدم
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: int
    is_admin: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# نماذج أنواع الحسابات
class AccountTypeBase(BaseModel):
    name: str
    name_ar: str
    code: str
    nature: str
    display_order: int = 0

class AccountTypeCreate(AccountTypeBase):
    pass

class AccountTypeResponse(AccountTypeBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

# نماذج الحسابات
class AccountBase(BaseModel):
    account_number: str
    name: str
    name_ar: str
    account_type_id: int
    parent_id: Optional[int] = None
    level: int = 1
    is_parent: bool = False
    is_postable: bool = True
    opening_balance: Decimal = Decimal('0.00')
    description: Optional[str] = None

class AccountCreate(AccountBase):
    pass

class AccountUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    parent_id: Optional[int] = None
    is_postable: Optional[bool] = None
    opening_balance: Optional[Decimal] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class AccountResponse(AccountBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# نماذج العملاء
class CustomerBase(BaseModel):
    customer_number: str
    name: str
    name_ar: str
    customer_type: str = "individual"
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Saudi Arabia"
    opening_balance: Decimal = Decimal('0.00')
    credit_limit: Decimal = Decimal('0.00')
    payment_terms: int = 30
    default_discount: Decimal = Decimal('0.00')
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    customer_type: Optional[str] = None
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    credit_limit: Optional[Decimal] = None
    payment_terms: Optional[int] = None
    default_discount: Optional[Decimal] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class CustomerResponse(CustomerBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# نماذج الموردين
class SupplierBase(BaseModel):
    supplier_number: str
    name: str
    name_ar: str
    supplier_type: str = "company"
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Saudi Arabia"
    opening_balance: Decimal = Decimal('0.00')
    credit_limit: Decimal = Decimal('0.00')
    payment_terms: int = 30
    default_discount: Decimal = Decimal('0.00')
    contact_person: Optional[str] = None
    contact_position: Optional[str] = None
    notes: Optional[str] = None

class SupplierCreate(SupplierBase):
    pass

class SupplierUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    supplier_type: Optional[str] = None
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    credit_limit: Optional[Decimal] = None
    payment_terms: Optional[int] = None
    default_discount: Optional[Decimal] = None
    contact_person: Optional[str] = None
    contact_position: Optional[str] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class SupplierResponse(SupplierBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# نماذج الاستجابة العامة
class MessageResponse(BaseModel):
    message: str
    message_ar: Optional[str] = None
    success: bool = True

class PaginatedResponse(BaseModel):
    items: List
    total: int
    page: int
    size: int
    pages: int
