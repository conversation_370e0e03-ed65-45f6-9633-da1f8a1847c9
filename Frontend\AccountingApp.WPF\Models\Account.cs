using System;

namespace AccountingApp.WPF.Models
{
    /// <summary>
    /// نموذج نوع الحساب
    /// Account Type Model
    /// </summary>
    public class AccountType
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Nature { get; set; } = string.Empty; // debit or credit
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// نموذج الحساب
    /// Account Model
    /// </summary>
    public class Account
    {
        public int Id { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public int AccountTypeId { get; set; }
        public int? ParentId { get; set; }
        public int Level { get; set; } = 1;
        public bool IsParent { get; set; } = false;
        public bool IsPostable { get; set; } = true;
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CurrentBalance { get; set; } = 0;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// الاسم المعروض
        /// Display Name
        /// </summary>
        public string DisplayName => $"{AccountNumber} - {Name}";

        /// <summary>
        /// الاسم المعروض بالعربية
        /// Display Name in Arabic
        /// </summary>
        public string DisplayNameAr => $"{AccountNumber} - {NameAr}";
    }

    /// <summary>
    /// نموذج إنشاء حساب جديد
    /// Create Account Model
    /// </summary>
    public class CreateAccountRequest
    {
        public string AccountNumber { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public int AccountTypeId { get; set; }
        public int? ParentId { get; set; }
        public int Level { get; set; } = 1;
        public bool IsParent { get; set; } = false;
        public bool IsPostable { get; set; } = true;
        public decimal OpeningBalance { get; set; } = 0;
        public string? Description { get; set; }
    }
}
