<Project>
  <PropertyGroup>
    <AssemblyName>AccountingApp.WPF</AssemblyName>
    <IntermediateOutputPath>obj\Debug\</IntermediateOutputPath>
    <BaseIntermediateOutputPath>obj\</BaseIntermediateOutputPath>
    <MSBuildProjectExtensionsPath>C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\</MSBuildProjectExtensionsPath>
    <_TargetAssemblyProjectName>AccountingApp.WPF</_TargetAssemblyProjectName>
    <RootNamespace>AccountingApp.WPF</RootNamespace>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\Accessibility.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\lib\net8.0\CommunityToolkit.Mvvm.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\Microsoft.CSharp.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\9.0.5\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\9.0.5\lib\net8.0\Microsoft.Extensions.Configuration.Binder.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\9.0.5\lib\net8.0\Microsoft.Extensions.Configuration.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.5\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.5\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\9.0.5\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics\9.0.5\lib\net8.0\Microsoft.Extensions.Diagnostics.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.http\9.0.5\lib\net8.0\Microsoft.Extensions.Http.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.5\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.5\lib\net8.0\Microsoft.Extensions.Logging.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options.configurationextensions\9.0.5\lib\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.5\lib\net8.0\Microsoft.Extensions.Options.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.5\lib\net8.0\Microsoft.Extensions.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\Microsoft.VisualBasic.Core.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\Microsoft.VisualBasic.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\Microsoft.Win32.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\Microsoft.Win32.Registry.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\Microsoft.Win32.Registry.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\Microsoft.Win32.SystemEvents.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\modernwpfui\0.9.6\lib\net5.0-windows7.0\ModernWpf.Controls.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\modernwpfui\0.9.6\lib\net5.0-windows7.0\ModernWpf.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\mscorlib.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\netstandard.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.3\lib\net6.0\Newtonsoft.Json.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationCore.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.Aero.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.Aero2.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.AeroLite.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.Classic.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.Luna.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationFramework.Royale.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\PresentationUI.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\ReachFramework.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.AppContext.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Buffers.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.CodeDom.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Collections.Concurrent.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Collections.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Collections.Immutable.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Collections.NonGeneric.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Collections.Specialized.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.Annotations.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.DataAnnotations.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.EventBasedAsync.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ComponentModel.TypeConverter.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Configuration.ConfigurationManager.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Configuration.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Console.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Core.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Data.Common.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Data.DataSetExtensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Data.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.Contracts.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.Debug.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.diagnostics.diagnosticsource\9.0.5\lib\net8.0\System.Diagnostics.DiagnosticSource.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.EventLog.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.FileVersionInfo.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.PerformanceCounter.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.Process.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.StackTrace.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.TextWriterTraceListener.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.Tools.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.TraceSource.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Diagnostics.Tracing.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.DirectoryServices.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Drawing.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Drawing.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Dynamic.Runtime.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Formats.Asn1.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Formats.Tar.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Globalization.Calendars.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Globalization.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Globalization.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Compression.Brotli.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Compression.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Compression.FileSystem.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Compression.ZipFile.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.FileSystem.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.FileSystem.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.FileSystem.DriveInfo.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.FileSystem.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.FileSystem.Watcher.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.IsolatedStorage.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.MemoryMappedFiles.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.IO.Packaging.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Pipes.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.Pipes.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.IO.UnmanagedMemoryStream.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Linq.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Linq.Expressions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Linq.Parallel.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Linq.Queryable.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Memory.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Http.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Http.Json.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.HttpListener.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Mail.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.NameResolution.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.NetworkInformation.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Ping.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Quic.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Requests.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Security.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.ServicePoint.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.Sockets.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.WebClient.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.WebHeaderCollection.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.WebProxy.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.WebSockets.Client.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Net.WebSockets.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Numerics.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Numerics.Vectors.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ObjectModel.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Printing.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.DispatchProxy.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Emit.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Emit.ILGeneration.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Emit.Lightweight.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Metadata.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Reflection.TypeExtensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Resources.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Resources.Reader.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Resources.ResourceManager.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Resources.Writer.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.CompilerServices.Unsafe.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.CompilerServices.VisualC.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Handles.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.InteropServices.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.InteropServices.JavaScript.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Intrinsics.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Loader.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Numerics.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Serialization.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Serialization.Formatters.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Serialization.Json.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Serialization.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Runtime.Serialization.Xml.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Claims.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Algorithms.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Cng.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Csp.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Encoding.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.OpenSsl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Pkcs.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.ProtectedData.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.X509Certificates.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Security.Cryptography.Xml.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Security.Permissions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Principal.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.Principal.Windows.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Security.SecureString.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ServiceModel.Web.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ServiceProcess.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.Encoding.CodePages.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.Encoding.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.Encoding.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.Encodings.Web.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.Json.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Text.RegularExpressions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Threading.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Channels.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Overlapped.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Tasks.Dataflow.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Tasks.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Tasks.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Tasks.Parallel.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Thread.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.ThreadPool.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Threading.Timer.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Transactions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Transactions.Local.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.ValueTuple.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Web.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Web.HttpUtility.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Windows.Controls.Ribbon.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Windows.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Windows.Extensions.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Windows.Input.Manipulations.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Windows.Presentation.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\System.Xaml.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.Linq.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.ReaderWriter.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.Serialization.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.XDocument.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.XmlDocument.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.XmlSerializer.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.XPath.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\ref\net8.0\System.Xml.XPath.XDocument.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\UIAutomationClient.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\UIAutomationClientSideProviders.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\UIAutomationProvider.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\UIAutomationTypes.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.16\ref\net8.0\WindowsBase.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\MainWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\AccountingMenuWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Accounting\AddAccountWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Accounting\AddJournalEntryWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Accounting\ChartOfAccountsWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Accounting\JournalEntriesWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Finance\AddCashBankAccountWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Finance\BankTransferWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Finance\CashBankWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Finance\CashMovementWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Finance\SimpleCashBankWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\AddCategoryWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\AddItemWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\CategoriesWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\ItemsWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\SimpleInventoryWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Inventory\UnitsWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\LoginWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Payments\CreateReceiptWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Payments\CustomerPaymentsWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Payments\PaymentsMainWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Purchases\AddSupplierWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Purchases\CreatePurchaseInvoiceWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Purchases\PurchaseInvoicesWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Purchases\SuppliersWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Reports\CustomersReportWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Reports\PurchasesReportWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Reports\ReportsMainWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Reports\SalesReportWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Sales\AddCustomerWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Sales\CreateSalesInvoiceWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Sales\CustomersWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Sales\DiscountWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\Sales\SalesInvoicesWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\SimpleLoginWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\Views\SimpleMainWindow.g.cs" />
    <Compile Include="C:\Users\<USER>\Desktop\mala\Frontend\AccountingApp.WPF\obj\Debug\net8.0-windows\App.g.cs" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="C:\Program Files\dotnet\sdk\9.0.300\Sdks\Microsoft.NET.Sdk\targets\..\analyzers\Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll" />
    <Analyzer Include="C:\Program Files\dotnet\sdk\9.0.300\Sdks\Microsoft.NET.Sdk\targets\..\analyzers\Microsoft.CodeAnalysis.NetAnalyzers.dll" />
    <Analyzer Include="C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\analyzers\dotnet\roslyn4.12\cs\CommunityToolkit.Mvvm.CodeFixers.dll" />
    <Analyzer Include="C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\analyzers\dotnet\roslyn4.12\cs\CommunityToolkit.Mvvm.SourceGenerators.dll" />
    <Analyzer Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.5\analyzers\dotnet\roslyn4.4\cs\Microsoft.Extensions.Logging.Generators.dll" />
    <Analyzer Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.5\analyzers\dotnet\roslyn4.4\cs\Microsoft.Extensions.Options.SourceGeneration.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/Microsoft.Interop.ComInterfaceGenerator.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/Microsoft.Interop.JavaScript.JSImportGenerator.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/Microsoft.Interop.LibraryImportGenerator.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/Microsoft.Interop.SourceGeneration.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/System.Text.Json.SourceGeneration.dll" />
    <Analyzer Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.16\analyzers/dotnet/cs/System.Text.RegularExpressions.Generator.dll" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
</Project>