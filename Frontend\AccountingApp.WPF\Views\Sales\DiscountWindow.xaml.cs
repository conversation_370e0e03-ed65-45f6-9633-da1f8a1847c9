using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Sales
{
    public partial class DiscountWindow : Window
    {
        public decimal DiscountPercent { get; private set; }
        public decimal DiscountAmount { get; private set; }
        
        private decimal _originalAmount;
        private bool _isPercentage = true;

        public DiscountWindow(decimal currentDiscountPercent = 0, decimal originalAmount = 100)
        {
            InitializeComponent();
            _originalAmount = originalAmount;
            DiscountPercent = currentDiscountPercent;
            
            DiscountValueTextBox.Text = currentDiscountPercent.ToString("F2");
            UpdatePreview();
        }

        private void DiscountTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DiscountTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _isPercentage = selectedItem.Tag?.ToString() == "Percentage";
                
                if (_isPercentage)
                {
                    DiscountValueLabel.Text = "نسبة الخصم %";
                    DiscountValueTextBox.Text = DiscountPercent.ToString("F2");
                }
                else
                {
                    DiscountValueLabel.Text = "مبلغ الخصم";
                    DiscountValueTextBox.Text = DiscountAmount.ToString("F2");
                }
                
                UpdatePreview();
            }
        }

        private void DiscountValueTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            if (!decimal.TryParse(DiscountValueTextBox.Text, out var discountValue))
            {
                discountValue = 0;
            }

            decimal calculatedDiscountAmount;
            decimal calculatedDiscountPercent;

            if (_isPercentage)
            {
                if (discountValue < 0 || discountValue > 100)
                {
                    ShowError("نسبة الخصم يجب أن تكون بين 0 و 100");
                    return;
                }
                
                calculatedDiscountPercent = discountValue;
                calculatedDiscountAmount = _originalAmount * (discountValue / 100);
            }
            else
            {
                if (discountValue < 0 || discountValue > _originalAmount)
                {
                    ShowError($"مبلغ الخصم يجب أن يكون بين 0 و {_originalAmount:F2}");
                    return;
                }
                
                calculatedDiscountAmount = discountValue;
                calculatedDiscountPercent = _originalAmount > 0 ? (discountValue / _originalAmount) * 100 : 0;
            }

            var finalAmount = _originalAmount - calculatedDiscountAmount;

            OriginalAmountLabel.Text = $"المبلغ الأصلي: {_originalAmount:F2}";
            DiscountAmountLabel.Text = $"قيمة الخصم: {calculatedDiscountAmount:F2}";
            FinalAmountLabel.Text = $"المبلغ النهائي: {finalAmount:F2}";

            // تحديث القيم للإرجاع
            DiscountPercent = calculatedDiscountPercent;
            DiscountAmount = calculatedDiscountAmount;

            HideError();
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorTextBlock.Visibility = Visibility.Collapsed;
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            if (!decimal.TryParse(DiscountValueTextBox.Text, out var discountValue))
            {
                ShowError("يرجى إدخال قيمة خصم صحيحة");
                return;
            }

            if (_isPercentage)
            {
                if (discountValue < 0 || discountValue > 100)
                {
                    ShowError("نسبة الخصم يجب أن تكون بين 0 و 100");
                    return;
                }
                DiscountPercent = discountValue;
            }
            else
            {
                if (discountValue < 0 || discountValue > _originalAmount)
                {
                    ShowError($"مبلغ الخصم يجب أن يكون بين 0 و {_originalAmount:F2}");
                    return;
                }
                DiscountPercent = _originalAmount > 0 ? (discountValue / _originalAmount) * 100 : 0;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
