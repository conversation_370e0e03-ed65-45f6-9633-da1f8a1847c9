using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AccountingApp.WPF.ViewModels
{
    /// <summary>
    /// ViewModel الأساسي
    /// Base ViewModel
    /// </summary>
    public abstract partial class BaseViewModel : ObservableObject
    {
        [ObservableProperty]
        private bool _isBusy;

        [ObservableProperty]
        private string _busyMessage = "جاري التحميل...";

        [ObservableProperty]
        private string _title = string.Empty;

        /// <summary>
        /// تنفيذ مهمة مع إظهار حالة التحميل
        /// Execute Task with Loading State
        /// </summary>
        protected async Task ExecuteAsync(Func<Task> operation, string? busyMessage = null)
        {
            if (IsBusy)
                return;

            try
            {
                IsBusy = true;
                if (!string.IsNullOrEmpty(busyMessage))
                    BusyMessage = busyMessage;

                await operation();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة مع إرجاع نتيجة
        /// Execute Task with Return Value
        /// </summary>
        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? busyMessage = null)
        {
            if (IsBusy)
                return default(T);

            try
            {
                IsBusy = true;
                if (!string.IsNullOrEmpty(busyMessage))
                    BusyMessage = busyMessage;

                return await operation();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
                return default(T);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// معالجة الأخطاء
        /// Handle Errors
        /// </summary>
        protected virtual async Task HandleErrorAsync(Exception ex)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                MessageBox.Show(
                    $"حدث خطأ: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            });
        }

        /// <summary>
        /// إظهار رسالة نجاح
        /// Show Success Message
        /// </summary>
        protected virtual async Task ShowSuccessAsync(string message)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                MessageBox.Show(
                    message,
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            });
        }

        /// <summary>
        /// إظهار رسالة تأكيد
        /// Show Confirmation Message
        /// </summary>
        protected virtual async Task<bool> ShowConfirmationAsync(string message, string title = "تأكيد")
        {
            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var result = MessageBox.Show(
                    message,
                    title,
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                return result == MessageBoxResult.Yes;
            });
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// Update UI
        /// </summary>
        protected void UpdateUI(Action action)
        {
            Application.Current.Dispatcher.Invoke(action);
        }

        /// <summary>
        /// تحديث واجهة المستخدم بشكل غير متزامن
        /// Update UI Async
        /// </summary>
        protected async Task UpdateUIAsync(Action action)
        {
            await Application.Current.Dispatcher.InvokeAsync(action);
        }
    }
}
