<Window x:Class="AccountingApp.WPF.Views.Finance.CashBankWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الصندوق والبنوك - Cash and Bank Management"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#17A2B8" Padding="15">
            <Grid>
                <TextBlock Text="إدارة الصندوق والبنوك - Cash and Bank Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>

                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="حساب جديد"
                       Background="#28A745"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="AddAccount_Click"/>

                <Button Content="تعديل"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="EditAccount_Click"/>

                <Button Content="حذف"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="DeleteAccount_Click"/>

                <Separator Margin="10,0"/>

                <Button Content="حركة نقدية"
                       Background="#FFC107"
                       Foreground="Black"
                       Padding="15,8"
                       Margin="5"
                       Click="CashMovement_Click"/>

                <Button Content="تحويل بنكي"
                       Background="#6F42C1"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="BankTransfer_Click"/>

                <Separator Margin="10,0"/>

                <ComboBox x:Name="AccountTypeFilterComboBox"
                         Width="150"
                         Margin="5"
                         SelectionChanged="AccountTypeFilter_Changed">
                    <ComboBoxItem Content="جميع الحسابات" IsSelected="True"/>
                    <ComboBoxItem Content="صندوق"/>
                    <ComboBoxItem Content="بنك"/>
                </ComboBox>

                <Button Content="تحديث"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="15,8"
                       Margin="5"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Accounts DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid x:Name="AccountsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     FontSize="12"
                     SelectionChanged="AccountsDataGrid_SelectionChanged">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="120"/>
                    <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="200"/>
                    <DataGridTextColumn Header="النوع" Binding="{Binding AccountTypeText}" Width="100"/>
                    <DataGridTextColumn Header="البنك" Binding="{Binding BankName}" Width="150"/>
                    <DataGridTextColumn Header="رقم الحساب البنكي" Binding="{Binding BankAccountNumber}" Width="150"/>
                    <DataGridTextColumn Header="العملة" Binding="{Binding Currency}" Width="80"/>
                    <DataGridTextColumn Header="الرصيد الحالي" Binding="{Binding CurrentBalance, StringFormat=C}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="Foreground" Value="{Binding BalanceColor}"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="آخر حركة" Binding="{Binding LastTransactionDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding StatusColor}"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock"
                          Text="جاهز - اختر حساب للعرض أو التعديل"
                          HorizontalAlignment="Right"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock x:Name="AccountCountTextBlock" Text="عدد الحسابات: 0" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalCashTextBlock" Text="إجمالي النقدية: 0.00" Foreground="Green" FontWeight="Bold" Margin="0,0,20,0"/>
                    <TextBlock x:Name="TotalBankTextBlock" Text="إجمالي البنوك: 0.00" Foreground="Blue" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
