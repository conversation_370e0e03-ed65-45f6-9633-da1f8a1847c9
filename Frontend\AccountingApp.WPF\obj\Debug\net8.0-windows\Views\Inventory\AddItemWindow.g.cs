﻿#pragma checksum "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "47F3BBEB710CFAE730E359683DE0E10C19D068DE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingApp.WPF.Views.Inventory {
    
    
    /// <summary>
    /// AddItemWindow
    /// </summary>
    public partial class AddItemWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ItemNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ItemTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ItemNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ItemNameArTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UnitComboBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SellingPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockInfoLabel;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrentQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ManufacturerTextBox;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsSaleableCheckBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsPurchasableCheckBox;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorTextBlock;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingApp.WPF;component/views/inventory/additemwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ItemNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ItemTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 54 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
            this.ItemTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ItemTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ItemNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ItemNameArTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.BarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.UnitComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SellingPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.StockInfoLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.StockInfoPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.CurrentQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.MinimumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.ManufacturerTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.ModelNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.IsSaleableCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.IsPurchasableCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.ErrorTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 261 "..\..\..\..\..\Views\Inventory\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

