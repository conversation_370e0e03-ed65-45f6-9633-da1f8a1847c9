"""
نموذج المستخدم
User Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from database.config import Base

class User(Base):
    """
    نموذج المستخدم
    User model for authentication and authorization
    """
    __tablename__ = "users"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # اسم المستخدم (فريد)
    username = Column(String(50), unique=True, index=True, nullable=False)
    
    # البريد الإلكتروني (فريد)
    email = Column(String(100), unique=True, index=True, nullable=False)
    
    # كلمة المرور المشفرة
    hashed_password = Column(String(255), nullable=False)
    
    # الاسم الكامل
    full_name = Column(String(100), nullable=False)
    
    # رقم الهاتف
    phone = Column(String(20), nullable=True)
    
    # هل المستخدم نشط
    is_active = Column(Boolean, default=True)
    
    # هل المستخدم مدير
    is_admin = Column(Boolean, default=False)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # آخر تسجيل دخول
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # ملاحظات
    notes = Column(Text, nullable=True)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', full_name='{self.full_name}')>"
