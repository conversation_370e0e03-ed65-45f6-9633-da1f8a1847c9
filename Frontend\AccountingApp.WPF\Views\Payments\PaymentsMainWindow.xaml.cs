using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace AccountingApp.WPF.Views.Payments
{
    public partial class PaymentsMainWindow : Window
    {
        public PaymentsMainWindow()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private void LoadDashboardData()
        {
            // محاكاة تحميل الإحصائيات السريعة
            TodayReceiptsTextBlock.Text = "15,500 ريال";
            TodayPaymentsTextBlock.Text = "8,200 ريال";
            NetCashFlowTextBlock.Text = "+7,300 ريال";
            PendingPaymentsTextBlock.Text = "12,800 ريال";

            LoadRecentTransactions();
            StatusTextBlock.Text = "تم تحميل البيانات بنجاح";
        }

        private void LoadRecentTransactions()
        {
            var recentTransactions = new ObservableCollection<PaymentTransactionViewModel>
            {
                new PaymentTransactionViewModel
                {
                    TransactionDate = DateTime.Today,
                    Type = "Receipt",
                    PartyName = "شركة الأعمال المتقدمة",
                    InvoiceNumber = "INV-2024-001",
                    Amount = 9775,
                    PaymentMethod = "تحويل بنكي",
                    Status = "Completed"
                },
                new PaymentTransactionViewModel
                {
                    TransactionDate = DateTime.Today,
                    Type = "Payment",
                    PartyName = "شركة التوريدات المتقدمة",
                    InvoiceNumber = "PUR-2024-001",
                    Amount = 9200,
                    PaymentMethod = "شيك",
                    Status = "Completed"
                },
                new PaymentTransactionViewModel
                {
                    TransactionDate = DateTime.Today.AddDays(-1),
                    Type = "Receipt",
                    PartyName = "مؤسسة التجارة الحديثة",
                    InvoiceNumber = "INV-2024-002",
                    Amount = 10000,
                    PaymentMethod = "نقدي",
                    Status = "Completed"
                },
                new PaymentTransactionViewModel
                {
                    TransactionDate = DateTime.Today.AddDays(-1),
                    Type = "Payment",
                    PartyName = "مؤسسة الإمدادات الصناعية",
                    InvoiceNumber = "PUR-2024-002",
                    Amount = 7250,
                    PaymentMethod = "تحويل بنكي",
                    Status = "Pending"
                },
                new PaymentTransactionViewModel
                {
                    TransactionDate = DateTime.Today.AddDays(-2),
                    Type = "Receipt",
                    PartyName = "شركة الخدمات المتكاملة",
                    InvoiceNumber = "INV-2024-003",
                    Amount = 6325,
                    PaymentMethod = "شيك",
                    Status = "Pending"
                }
            };

            RecentTransactionsDataGrid.ItemsSource = recentTransactions;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void CustomerPayments_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customerPaymentsWindow = new CustomerPaymentsWindow();
                customerPaymentsWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة مقبوضات العملاء";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة مقبوضات العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SupplierPayments_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierPaymentsWindow = new SupplierPaymentsWindow();
                supplierPaymentsWindow.ShowDialog();
                StatusTextBlock.Text = "تم فتح نافذة مدفوعات الموردين";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة مدفوعات الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CashTransactions_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير المعاملات النقدية قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "المعاملات النقدية - قيد التطوير";
        }

        private void PaymentReports_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تقارير المدفوعات قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "تقارير المدفوعات - قيد التطوير";
        }

        private void NewReceipt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newReceiptWindow = new CreateReceiptWindow();
                if (newReceiptWindow.ShowDialog() == true)
                {
                    LoadDashboardData(); // تحديث البيانات
                    StatusTextBlock.Text = "تم تسجيل مقبوض جديد";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل المقبوض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NewPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newPaymentWindow = new CreatePaymentWindow();
                if (newPaymentWindow.ShowDialog() == true)
                {
                    LoadDashboardData(); // تحديث البيانات
                    StatusTextBlock.Text = "تم تسجيل دفعة جديدة";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BankTransfer_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير التحويل البنكي قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "التحويل البنكي - قيد التطوير";
        }

        private void AccountReconciliation_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تسوية الحسابات قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusTextBlock.Text = "تسوية الحسابات - قيد التطوير";
        }
    }

    public class PaymentTransactionViewModel
    {
        public DateTime TransactionDate { get; set; }
        public string Type { get; set; } = "";
        public string PartyName { get; set; } = "";
        public string InvoiceNumber { get; set; } = "";
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = "";
        public string Status { get; set; } = "";

        public string TypeText => Type switch
        {
            "Receipt" => "مقبوض",
            "Payment" => "دفعة",
            "Transfer" => "تحويل",
            _ => "غير محدد"
        };

        public string TypeColor => Type switch
        {
            "Receipt" => "Green",
            "Payment" => "Red",
            "Transfer" => "Blue",
            _ => "Black"
        };

        public string AmountColor => Type switch
        {
            "Receipt" => "Green",
            "Payment" => "Red",
            "Transfer" => "Blue",
            _ => "Black"
        };

        public string StatusText => Status switch
        {
            "Completed" => "مكتمل",
            "Pending" => "معلق",
            "Cancelled" => "ملغي",
            _ => "غير محدد"
        };

        public string StatusColor => Status switch
        {
            "Completed" => "Green",
            "Pending" => "Orange",
            "Cancelled" => "Red",
            _ => "Black"
        };
    }
}
