using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;

namespace AccountingApp.WPF.Views.Reports
{
    public partial class PurchasesReportWindow : Window
    {
        private readonly string _reportType;
        private ObservableCollection<PurchaseReportItem> _purchasesData;

        public PurchasesReportWindow(string reportType = "daily")
        {
            InitializeComponent();
            _reportType = reportType;
            
            SetupWindow();
            SetDefaultDates();
            LoadSampleData();
        }

        private void SetupWindow()
        {
            switch (_reportType)
            {
                case "daily":
                    HeaderTextBlock.Text = "تقرير المشتريات اليومية - Daily Purchases Report";
                    Title = "تقرير المشتريات اليومية";
                    break;
                case "monthly":
                    HeaderTextBlock.Text = "تقرير المشتريات الشهرية - Monthly Purchases Report";
                    Title = "تقرير المشتريات الشهرية";
                    break;
                default:
                    HeaderTextBlock.Text = "تقرير المشتريات - Purchases Report";
                    break;
            }
        }

        private void SetDefaultDates()
        {
            switch (_reportType)
            {
                case "daily":
                    FromDatePicker.SelectedDate = DateTime.Today;
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
                case "monthly":
                    FromDatePicker.SelectedDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
                default:
                    FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
                    ToDatePicker.SelectedDate = DateTime.Today;
                    break;
            }
        }

        private void LoadSampleData()
        {
            _purchasesData = new ObservableCollection<PurchaseReportItem>
            {
                new PurchaseReportItem
                {
                    InvoiceNumber = "PUR-2024-001",
                    InvoiceDate = DateTime.Today,
                    SupplierName = "شركة التوريدات المتقدمة",
                    SupplierInvoiceNumber = "SUP-INV-001",
                    SubTotal = 8000,
                    TaxAmount = 1200,
                    TotalAmount = 9200,
                    PaidAmount = 9200,
                    RemainingAmount = 0,
                    Status = "Paid"
                },
                new PurchaseReportItem
                {
                    InvoiceNumber = "PUR-2024-002",
                    InvoiceDate = DateTime.Today.AddDays(-1),
                    SupplierName = "مؤسسة الإمدادات الصناعية",
                    SupplierInvoiceNumber = "IND-2024-045",
                    SubTotal = 15000,
                    TaxAmount = 2250,
                    TotalAmount = 17250,
                    PaidAmount = 10000,
                    RemainingAmount = 7250,
                    Status = "PartiallyPaid"
                },
                new PurchaseReportItem
                {
                    InvoiceNumber = "PUR-2024-003",
                    InvoiceDate = DateTime.Today.AddDays(-2),
                    SupplierName = "شركة المواد الخام",
                    SupplierInvoiceNumber = "RAW-MAT-123",
                    SubTotal = 5500,
                    TaxAmount = 825,
                    TotalAmount = 6325,
                    PaidAmount = 0,
                    RemainingAmount = 6325,
                    Status = "Pending"
                },
                new PurchaseReportItem
                {
                    InvoiceNumber = "PUR-2024-004",
                    InvoiceDate = DateTime.Today.AddDays(-3),
                    SupplierName = "مكتب الخدمات التقنية",
                    SupplierInvoiceNumber = "TECH-SRV-089",
                    SubTotal = 3200,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 3680,
                    RemainingAmount = 0,
                    Status = "Paid"
                }
            };

            PurchasesDataGrid.ItemsSource = _purchasesData;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleData = PurchasesDataGrid.ItemsSource as ObservableCollection<PurchaseReportItem> ?? _purchasesData;
            
            InvoiceCountTextBlock.Text = visibleData.Count.ToString();
            TotalPurchasesTextBlock.Text = visibleData.Sum(p => p.TotalAmount).ToString("F2");
            TotalTaxTextBlock.Text = visibleData.Sum(p => p.TaxAmount).ToString("F2");
            TotalPaidTextBlock.Text = visibleData.Sum(p => p.PaidAmount).ToString("F2");
            TotalRemainingTextBlock.Text = visibleData.Sum(p => p.RemainingAmount).ToString("F2");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddDays(-30);
            var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;
            var selectedSupplier = SupplierFilterComboBox.SelectedItem?.ToString() ?? "جميع الموردين";

            var filteredData = _purchasesData.Where(p =>
                p.InvoiceDate >= fromDate &&
                p.InvoiceDate <= toDate &&
                (selectedSupplier == "جميع الموردين" || p.SupplierName == selectedSupplier)
            ).ToList();

            PurchasesDataGrid.ItemsSource = new ObservableCollection<PurchaseReportItem>(filteredData);
            UpdateSummary();
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة التقرير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير إلى Excel", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToPDF_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير إلى PDF", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    public class PurchaseReportItem
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; } = "";
        public string SupplierInvoiceNumber { get; set; } = "";
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; } = "";

        public string StatusText => Status switch
        {
            "Paid" => "مدفوعة",
            "PartiallyPaid" => "مدفوعة جزئياً",
            "Pending" => "معلقة",
            "Cancelled" => "ملغاة",
            _ => "غير محدد"
        };

        public string StatusColor => Status switch
        {
            "Paid" => "Green",
            "PartiallyPaid" => "Orange",
            "Pending" => "Red",
            "Cancelled" => "Gray",
            _ => "Black"
        };

        public string RemainingAmountColor => RemainingAmount > 0 ? "Red" : "Green";
    }
}
