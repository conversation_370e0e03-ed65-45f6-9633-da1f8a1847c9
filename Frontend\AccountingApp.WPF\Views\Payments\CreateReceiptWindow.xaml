<Window x:Class="AccountingApp.WPF.Views.Payments.CreateReceiptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء مقبوض - Create Receipt"
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#28A745" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إنشاء مقبوض جديد - Create New Receipt"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Receipt Information -->
                <TextBlock Text="معلومات المقبوض" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#28A745"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم المقبوض *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ReceiptNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تاريخ المقبوض *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="ReceiptDatePicker"
                                   Padding="8"
                                   FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Customer Information -->
                <TextBlock Text="معلومات العميل" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#28A745"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CustomerComboBox"
                                 Padding="8"
                                 FontSize="14"
                                 SelectionChanged="CustomerComboBox_SelectionChanged">
                            <ComboBoxItem Content="شركة الأعمال المتقدمة" Tag="CUS001"/>
                            <ComboBoxItem Content="مؤسسة التجارة الحديثة" Tag="CUS002"/>
                            <ComboBoxItem Content="شركة الخدمات المتكاملة" Tag="CUS003"/>
                            <ComboBoxItem Content="مكتب الاستشارات القانونية" Tag="CUS004"/>
                            <ComboBoxItem Content="شركة التطوير العقاري" Tag="CUS005"/>
                        </ComboBox>
                    </StackPanel>

                    <Button Grid.Column="2" Content="عميل جديد" 
                           Background="#007ACC" 
                           Foreground="White" 
                           Padding="10,8" 
                           VerticalAlignment="Bottom"
                           Click="AddCustomer_Click"/>
                </Grid>

                <!-- Invoice Information -->
                <TextBlock Text="معلومات الفاتورة" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#28A745"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الفاتورة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="InvoiceComboBox"
                                 Padding="8"
                                 FontSize="14"
                                 SelectionChanged="InvoiceComboBox_SelectionChanged">
                            <ComboBoxItem Content="INV-2024-001" Tag="9775"/>
                            <ComboBoxItem Content="INV-2024-002" Tag="17250"/>
                            <ComboBoxItem Content="INV-2024-003" Tag="6325"/>
                            <ComboBoxItem Content="INV-2024-004" Tag="3680"/>
                            <ComboBoxItem Content="INV-2024-005" Tag="13800"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="مبلغ الفاتورة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="InvoiceAmountTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>
                </Grid>

                <!-- Payment Information -->
                <TextBlock Text="معلومات الدفع" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#28A745"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المبلغ المستلم *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="AmountTextBox"
                                Padding="8"
                                FontSize="14"
                                TextChanged="AmountTextBox_TextChanged"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="طريقة الدفع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="PaymentMethodComboBox"
                                 Padding="8"
                                 FontSize="14"
                                 SelectionChanged="PaymentMethodComboBox_SelectionChanged">
                            <ComboBoxItem Content="نقدي" IsSelected="True"/>
                            <ComboBoxItem Content="تحويل بنكي"/>
                            <ComboBoxItem Content="شيك"/>
                            <ComboBoxItem Content="بطاقة ائتمان"/>
                            <ComboBoxItem Content="حوالة"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- Reference Information -->
                <Grid x:Name="ReferenceGrid" Margin="0,0,0,15" Visibility="Collapsed">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ReferenceLabel" Text="رقم المرجع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ReferenceNumberTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="البنك/الجهة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="BankTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Summary -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,10,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="مبلغ الفاتورة:" FontSize="12" FontWeight="SemiBold"/>
                            <TextBlock x:Name="SummaryInvoiceAmountTextBlock" Text="0.00 ريال" FontSize="14" Foreground="Blue"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="المبلغ المستلم:" FontSize="12" FontWeight="SemiBold"/>
                            <TextBlock x:Name="SummaryReceivedAmountTextBlock" Text="0.00 ريال" FontSize="14" Foreground="Green"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="المبلغ المتبقي:" FontSize="12" FontWeight="SemiBold"/>
                            <TextBlock x:Name="SummaryRemainingAmountTextBlock" Text="0.00 ريال" FontSize="14" Foreground="Red"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,10,0,0"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="حفظ وطباعة"
                       Background="#007ACC"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveAndPrint_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
