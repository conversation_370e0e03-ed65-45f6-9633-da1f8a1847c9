"""
نموذج الموردين
Supplier Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class Supplier(Base):
    """
    نموذج المورد
    Supplier Model
    """
    __tablename__ = "suppliers"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # رقم المورد
    supplier_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # اسم المورد
    name = Column(String(100), nullable=False)
    
    # اسم المورد بالعربية
    name_ar = Column(String(150), nullable=False)
    
    # نوع المورد (individual, company)
    supplier_type = Column(String(20), default='company')
    
    # رقم الهوية/السجل التجاري
    identification_number = Column(String(50), nullable=True)
    
    # الرقم الضريبي
    tax_number = Column(String(50), nullable=True)
    
    # رقم الهاتف الأساسي
    phone = Column(String(20), nullable=True)
    
    # رقم الهاتف الثانوي
    phone2 = Column(String(20), nullable=True)
    
    # البريد الإلكتروني
    email = Column(String(100), nullable=True)
    
    # العنوان
    address = Column(Text, nullable=True)
    
    # المدينة
    city = Column(String(50), nullable=True)
    
    # المنطقة/المحافظة
    region = Column(String(50), nullable=True)
    
    # الرمز البريدي
    postal_code = Column(String(10), nullable=True)
    
    # البلد
    country = Column(String(50), default='Saudi Arabia')
    
    # الرصيد الافتتاحي
    opening_balance = Column(Numeric(15, 2), default=0.00)
    
    # الرصيد الحالي
    current_balance = Column(Numeric(15, 2), default=0.00)
    
    # حد الائتمان
    credit_limit = Column(Numeric(15, 2), default=0.00)
    
    # مدة السداد بالأيام
    payment_terms = Column(Integer, default=30)
    
    # نسبة الخصم الافتراضية
    default_discount = Column(Numeric(5, 2), default=0.00)
    
    # اسم الشخص المسؤول
    contact_person = Column(String(100), nullable=True)
    
    # منصب الشخص المسؤول
    contact_position = Column(String(50), nullable=True)
    
    # ملاحظات
    notes = Column(Text, nullable=True)
    
    # هل المورد نشط
    is_active = Column(Boolean, default=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    purchase_invoices = relationship("PurchaseInvoice", back_populates="supplier")

    def __repr__(self):
        return f"<Supplier(id={self.id}, number='{self.supplier_number}', name='{self.name}')>"

    @property
    def display_name(self):
        """الاسم المعروض"""
        return f"{self.supplier_number} - {self.name}"

    @property
    def display_name_ar(self):
        """الاسم المعروض بالعربية"""
        return f"{self.supplier_number} - {self.name_ar}"
