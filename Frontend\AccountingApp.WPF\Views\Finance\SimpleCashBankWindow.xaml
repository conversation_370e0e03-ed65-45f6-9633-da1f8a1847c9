<Window x:Class="AccountingApp.WPF.Views.Finance.SimpleCashBankWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الصندوق والبنوك - Cash and Bank Management"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#6F42C1" Padding="15">
            <Grid>
                <TextBlock Text="إدارة الصندوق والبنوك - Cash and Bank Management"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>

                <Button Content="إغلاق"
                       Background="#DC3545"
                       Foreground="White"
                       Padding="10,5"
                       HorizontalAlignment="Left"
                       Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Menu Buttons -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20">
            <StackPanel>
                <TextBlock Text="اختر الوحدة التي تريد إدارتها:"
                          FontSize="14"
                          FontWeight="SemiBold"
                          Margin="0,0,0,15"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="إدارة الحسابات المصرفية"
                           Background="#28A745"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageBankAccounts_Click"/>

                    <Button Content="إدارة الصندوق"
                           Background="#17A2B8"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageCash_Click"/>

                    <Button Content="الحركات المالية"
                           Background="#E83E8C"
                           Foreground="White"
                           Padding="20,15"
                           Margin="10"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Click="ManageTransactions_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Quick Summary -->
        <Border Grid.Row="2" Background="White" Padding="20">
            <StackPanel>
                <TextBlock Text="ملخص سريع للحسابات المالية:"
                          FontSize="16"
                          FontWeight="Bold"
                          Margin="0,0,0,20"
                          Foreground="#6F42C1"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Cash Balance -->
                    <Border Grid.Column="0" Background="#E8F5E8" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="رصيد الصندوق"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      HorizontalAlignment="Center"
                                      Foreground="#388E3C"/>
                            <TextBlock x:Name="CashBalanceTextBlock"
                                      Text="25,500 ريال"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="#388E3C"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Bank Balance -->
                    <Border Grid.Column="1" Background="#E3F2FD" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="رصيد البنوك"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      HorizontalAlignment="Center"
                                      Foreground="#1976D2"/>
                            <TextBlock x:Name="BankBalanceTextBlock"
                                      Text="185,750 ريال"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="#1976D2"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Total Balance -->
                    <Border Grid.Column="2" Background="#FFF3E0" Padding="20" Margin="10" CornerRadius="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي الأرصدة"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      HorizontalAlignment="Center"
                                      Foreground="#F57C00"/>
                            <TextBlock x:Name="TotalBalanceTextBlock"
                                      Text="211,250 ريال"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="#F57C00"
                                      Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Recent Transactions -->
                <TextBlock Text="آخر الحركات المالية:"
                          FontSize="14"
                          FontWeight="SemiBold"
                          Margin="0,30,0,10"/>

                <ListBox x:Name="RecentTransactionsListBox"
                        Height="150"
                        Background="#F8F9FA"
                        BorderBrush="#DEE2E6"
                        BorderThickness="1">
                    <ListBoxItem Content="إيداع نقدي - 5,000 ريال - اليوم"/>
                    <ListBoxItem Content="سحب من البنك - 2,500 ريال - أمس"/>
                    <ListBoxItem Content="تحويل بنكي - 10,000 ريال - منذ يومين"/>
                    <ListBoxItem Content="دفع فاتورة - 3,200 ريال - منذ 3 أيام"/>
                    <ListBoxItem Content="استلام شيك - 8,500 ريال - منذ 4 أيام"/>
                </ListBox>

                <!-- Quick Actions -->
                <TextBlock Text="إجراءات سريعة:"
                          FontSize="14"
                          FontWeight="SemiBold"
                          Margin="0,20,0,10"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="إيداع نقدي"
                           Background="#28A745"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="CashDeposit_Click"/>

                    <Button Content="سحب نقدي"
                           Background="#DC3545"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="CashWithdraw_Click"/>

                    <Button Content="تحويل بنكي"
                           Background="#007ACC"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="BankTransfer_Click"/>

                    <Button Content="دفع فاتورة"
                           Background="#6F42C1"
                           Foreground="White"
                           Padding="15,8"
                           Margin="5"
                           Click="PayBill_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#E9ECEF" Padding="10">
            <Grid>
                <TextBlock x:Name="StatusTextBlock"
                          Text="جاهز - اختر وحدة للبدء"
                          HorizontalAlignment="Right"/>
                <TextBlock Text="نظام إدارة الصندوق والبنوك"
                          Foreground="Green"
                          HorizontalAlignment="Left"/>
            </Grid>
        </Border>
    </Grid>
</Window>
