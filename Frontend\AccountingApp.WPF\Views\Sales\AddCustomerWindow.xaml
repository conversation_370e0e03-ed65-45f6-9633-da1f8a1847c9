<Window x:Class="AccountingApp.WPF.Views.Sales.AddCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة عميل جديد - Add New Customer"
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة عميل جديد - Add New Customer"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CustomerNumberTextBox"
                                Padding="8"
                                FontSize="14"
                                IsReadOnly="True"
                                Background="#E9ECEF"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="نوع العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CustomerTypeComboBox"
                                 Padding="8"
                                 FontSize="14">
                            <ComboBoxItem Content="فرد - Individual" Tag="Individual"/>
                            <ComboBoxItem Content="شركة - Company" Tag="Company" IsSelected="True"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اسم العميل (إنجليزي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CustomerNameTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="اسم العميل (عربي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CustomerNameArTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Contact Information -->
                <TextBlock Text="معلومات الاتصال" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الهاتف الأساسي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الهاتف الثانوي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="Phone2TextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Address Information -->
                <TextBlock Text="معلومات العنوان" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <TextBlock Text="العنوان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="60"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        Margin="0,0,0,15"/>

                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المدينة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CityTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="المنطقة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="RegionTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="4">
                        <TextBlock Text="الرمز البريدي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PostalCodeTextBox"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Financial Information -->
                <TextBlock Text="المعلومات المالية" FontSize="14" FontWeight="Bold" Margin="0,15,0,10" Foreground="#007ACC"/>
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الرصيد الافتتاحي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="OpeningBalanceTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="حد الائتمان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CreditLimitTextBox"
                                Text="0.00"
                                Padding="8"
                                FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Status -->
                <CheckBox x:Name="IsActiveCheckBox"
                         Content="نشط"
                         IsChecked="True"
                         Margin="0,0,0,15"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,0,0,10"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
