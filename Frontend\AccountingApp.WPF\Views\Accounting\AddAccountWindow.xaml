<Window x:Class="AccountingApp.WPF.Views.Accounting.AddAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب جديد - Add New Account"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="15">
            <TextBlock x:Name="HeaderTextBlock"
                      Text="إضافة حساب جديد - Add New Account"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Account Number -->
                <TextBlock Text="رقم الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNumberTextBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Account Name (English) -->
                <TextBlock Text="اسم الحساب (إنجليزي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNameTextBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Account Name (Arabic) -->
                <TextBlock Text="اسم الحساب (عربي) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNameArTextBox"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Account Type -->
                <TextBlock Text="نوع الحساب *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="AccountTypeComboBox"
                         Padding="8"
                         FontSize="14"
                         Margin="0,0,0,15">
                    <ComboBoxItem Content="أصول - Assets" Tag="Assets"/>
                    <ComboBoxItem Content="خصوم - Liabilities" Tag="Liabilities"/>
                    <ComboBoxItem Content="حقوق ملكية - Equity" Tag="Equity"/>
                    <ComboBoxItem Content="إيرادات - Revenue" Tag="Revenue"/>
                    <ComboBoxItem Content="مصروفات - Expenses" Tag="Expenses"/>
                </ComboBox>

                <!-- Parent Account -->
                <TextBlock Text="الحساب الأب" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="ParentAccountComboBox"
                         Padding="8"
                         FontSize="14"
                         Margin="0,0,0,15"
                         DisplayMemberPath="DisplayName"/>

                <!-- Opening Balance -->
                <TextBlock Text="الرصيد الافتتاحي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="OpeningBalanceTextBox"
                        Text="0.00"
                        Padding="8"
                        FontSize="14"
                        Margin="0,0,0,15"/>

                <!-- Description -->
                <TextBlock Text="الوصف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox"
                        Padding="8"
                        FontSize="14"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"
                        Margin="0,0,0,15"/>

                <!-- Options -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                    <CheckBox x:Name="IsParentCheckBox"
                             Content="حساب أب (يحتوي على حسابات فرعية)"
                             Margin="0,0,20,0"/>
                    
                    <CheckBox x:Name="IsActiveCheckBox"
                             Content="نشط"
                             IsChecked="True"/>
                </StackPanel>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          FontSize="12"
                          TextWrapping="Wrap"
                          Margin="0,0,0,10"
                          Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="SaveButton_Click"/>
                
                <Button Content="إلغاء"
                       Background="#6C757D"
                       Foreground="White"
                       Padding="20,10"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
