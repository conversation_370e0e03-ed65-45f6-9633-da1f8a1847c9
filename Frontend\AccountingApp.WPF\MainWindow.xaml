﻿<Window x:Class="AccountingApp.WPF.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AccountingApp.WPF.Views"
        mc:Ignorable="d"
        Title="نظام المحاسبة المالية المتكامل"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="MenuButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#007ACC" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="نظام المحاسبة المالية المتكامل"
                              FontSize="18"
                              FontWeight="Bold"
                              Foreground="White"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="v1.0"
                              FontSize="12"
                              Foreground="#E0E0E0"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding CurrentUser.FullName}"
                              FontSize="14"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="0,0,15,0"/>
                    <Button Content="تسجيل الخروج"
                           Style="{StaticResource ModernButton}"
                           Background="#DC3545"
                           Command="{Binding LogoutCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Sidebar Menu -->
        <Border Grid.Row="1" Grid.Column="0" Background="#2C3E50">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- المحاسبة العامة -->
                    <Expander Header="المحاسبة العامة" IsExpanded="True" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="دليل الحسابات" Style="{StaticResource MenuButton}"/>
                            <Button Content="القيود اليومية" Style="{StaticResource MenuButton}"/>
                            <Button Content="دفتر الأستاذ" Style="{StaticResource MenuButton}"/>
                            <Button Content="ميزان المراجعة" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- المبيعات والعملاء -->
                    <Expander Header="المبيعات والعملاء" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="إدارة العملاء" Style="{StaticResource MenuButton}"/>
                            <Button Content="فواتير المبيعات" Style="{StaticResource MenuButton}"/>
                            <Button Content="مرتجعات المبيعات" Style="{StaticResource MenuButton}"/>
                            <Button Content="كشوف حسابات العملاء" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- المشتريات والموردين -->
                    <Expander Header="المشتريات والموردين" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="إدارة الموردين" Style="{StaticResource MenuButton}"/>
                            <Button Content="فواتير المشتريات" Style="{StaticResource MenuButton}"/>
                            <Button Content="مرتجعات المشتريات" Style="{StaticResource MenuButton}"/>
                            <Button Content="كشوف حسابات الموردين" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- المخازن والأصناف -->
                    <Expander Header="المخازن والأصناف" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="إدارة الأصناف" Style="{StaticResource MenuButton}"/>
                            <Button Content="إدارة المخازن" Style="{StaticResource MenuButton}"/>
                            <Button Content="حركات المخزون" Style="{StaticResource MenuButton}"/>
                            <Button Content="الجرد" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- الصندوق والبنوك -->
                    <Expander Header="الصندوق والبنوك" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="إدارة الحسابات النقدية" Style="{StaticResource MenuButton}"/>
                            <Button Content="سندات القبض" Style="{StaticResource MenuButton}"/>
                            <Button Content="سندات الصرف" Style="{StaticResource MenuButton}"/>
                            <Button Content="التحويلات" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- التقارير -->
                    <Expander Header="التقارير المالية" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="قائمة الدخل" Style="{StaticResource MenuButton}"/>
                            <Button Content="الميزانية العمومية" Style="{StaticResource MenuButton}"/>
                            <Button Content="قائمة التدفقات النقدية" Style="{StaticResource MenuButton}"/>
                            <Button Content="تقارير مقارنة" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>

                    <!-- الإعدادات -->
                    <Expander Header="الإعدادات" Foreground="White" FontWeight="Bold">
                        <StackPanel>
                            <Button Content="إعدادات الشركة" Style="{StaticResource MenuButton}"/>
                            <Button Content="إدارة المستخدمين" Style="{StaticResource MenuButton}"/>
                            <Button Content="النسخ الاحتياطي" Style="{StaticResource MenuButton}"/>
                            <Button Content="حول النظام" Style="{StaticResource MenuButton}"/>
                        </StackPanel>
                    </Expander>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Main Content Area -->
        <Border Grid.Row="1" Grid.Column="1" Background="#F8F9FA" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Welcome Section -->
                <Border Grid.Row="0" Background="White" CornerRadius="10" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="مرحباً بك في نظام المحاسبة المالية المتكامل"
                                  FontSize="24"
                                  FontWeight="Bold"
                                  Foreground="#2C3E50"
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="اختر من القائمة الجانبية للبدء في استخدام النظام"
                                  FontSize="16"
                                  Foreground="#6C757D"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Content Frame -->
                <Frame Grid.Row="1" x:Name="MainContentFrame" NavigationUIVisibility="Hidden"/>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#E9ECEF" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="جاهز" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="{Binding CurrentDateTime}" VerticalAlignment="Center" Margin="20,0"/>
                <TextBlock Grid.Column="2" Text="متصل" Foreground="Green" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
