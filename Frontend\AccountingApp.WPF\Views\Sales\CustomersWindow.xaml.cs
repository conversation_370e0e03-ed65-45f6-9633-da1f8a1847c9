using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Sales
{
    public partial class CustomersWindow : Window
    {
        private ObservableCollection<CustomerViewModel> _customers;
        private CustomerViewModel _selectedCustomer;

        public CustomersWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _customers = new ObservableCollection<CustomerViewModel>
            {
                new CustomerViewModel
                {
                    CustomerNumber = "C001",
                    Name = "Ahmed Trading Company",
                    NameAr = "شركة أحمد التجارية",
                    CustomerType = "Company",
                    Phone = "***********",
                    Email = "<EMAIL>",
                    City = "الرياض",
                    CurrentBalance = 15000,
                    IsActive = true
                },
                new CustomerViewModel
                {
                    CustomerNumber = "C002",
                    Name = "Fatima Retail Store",
                    NameAr = "متجر فاطمة للتجزئة",
                    CustomerType = "Individual",
                    Phone = "01987654321",
                    Email = "<EMAIL>",
                    City = "جدة",
                    CurrentBalance = -2500,
                    IsActive = true
                },
                new CustomerViewModel
                {
                    CustomerNumber = "C003",
                    Name = "Modern Electronics Co.",
                    NameAr = "شركة الإلكترونيات الحديثة",
                    CustomerType = "Company",
                    Phone = "01122334455",
                    Email = "<EMAIL>",
                    City = "الدمام",
                    CurrentBalance = 8750,
                    IsActive = true
                },
                new CustomerViewModel
                {
                    CustomerNumber = "C004",
                    Name = "Omar Construction",
                    NameAr = "مؤسسة عمر للمقاولات",
                    CustomerType = "Company",
                    Phone = "01555666777",
                    Email = "<EMAIL>",
                    City = "مكة المكرمة",
                    CurrentBalance = 25000,
                    IsActive = false
                },
                new CustomerViewModel
                {
                    CustomerNumber = "C005",
                    Name = "Aisha Fashion Boutique",
                    NameAr = "بوتيك عائشة للأزياء",
                    CustomerType = "Individual",
                    Phone = "01777888999",
                    Email = "<EMAIL>",
                    City = "المدينة المنورة",
                    CurrentBalance = 5200,
                    IsActive = true
                }
            };

            CustomersDataGrid.ItemsSource = _customers;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            CustomerCountTextBlock.Text = $"عدد العملاء: {_customers.Count}";
            var totalBalance = _customers.Sum(c => c.CurrentBalance);
            TotalBalanceTextBlock.Text = $"إجمالي الأرصدة: {totalBalance:C}";
            TotalBalanceTextBlock.Foreground = totalBalance >= 0 ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddCustomer_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddCustomerWindow();
            if (addWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة العميل بنجاح";
            }
        }

        private void EditCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddCustomerWindow(_selectedCustomer);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل العميل بنجاح";
            }
        }

        private void DeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف العميل '{_selectedCustomer.Name}'؟",
                                       "تأكيد الحذف",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _customers.Remove(_selectedCustomer);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف العميل بنجاح";
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التصدير قريباً!", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";

            if (string.IsNullOrWhiteSpace(searchText))
            {
                CustomersDataGrid.ItemsSource = _customers;
            }
            else
            {
                var filteredCustomers = _customers.Where(c =>
                    c.Name.ToLower().Contains(searchText) ||
                    c.NameAr.Contains(searchText) ||
                    c.CustomerNumber.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    c.Email.ToLower().Contains(searchText) ||
                    c.City.Contains(searchText)
                ).ToList();

                CustomersDataGrid.ItemsSource = new ObservableCollection<CustomerViewModel>(filteredCustomers);
            }
        }

        private void SalesInvoices_Click(object sender, RoutedEventArgs e)
        {
            var salesInvoicesWindow = new SalesInvoicesWindow();
            salesInvoicesWindow.ShowDialog();
        }

        private void CustomersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCustomer = CustomersDataGrid.SelectedItem as CustomerViewModel;

            if (_selectedCustomer != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedCustomer.CustomerNumber} - {_selectedCustomer.Name}";
            }
        }
    }

    public class CustomerViewModel
    {
        public string CustomerNumber { get; set; } = "";
        public string Name { get; set; } = "";
        public string NameAr { get; set; } = "";
        public string CustomerType { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string City { get; set; } = "";
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }

        public string CustomerTypeText => CustomerType == "Company" ? "شركة" : "فرد";
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
        public string BalanceColor => CurrentBalance >= 0 ? "Green" : "Red";
    }
}
