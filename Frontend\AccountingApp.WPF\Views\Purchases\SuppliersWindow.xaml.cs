using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AccountingApp.WPF.Views.Purchases
{
    public partial class SuppliersWindow : Window
    {
        private ObservableCollection<SupplierViewModel> _suppliers;
        private SupplierViewModel _selectedSupplier;

        public SuppliersWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            _suppliers = new ObservableCollection<SupplierViewModel>
            {
                new SupplierViewModel
                {
                    SupplierNumber = "SUP001",
                    Name = "شركة التوريدات المتقدمة",
                    TradeName = "Advanced Supplies Co.",
                    Phone = "011-4567890",
                    Email = "<EMAIL>",
                    City = "الرياض",
                    CurrentBalance = -15000,
                    LastTransactionDate = DateTime.Today.AddDays(-3),
                    IsActive = true
                },
                new SupplierViewModel
                {
                    SupplierNumber = "SUP002",
                    Name = "مؤسسة الإمدادات الصناعية",
                    TradeName = "Industrial Supplies Est.",
                    Phone = "012-3456789",
                    Email = "<EMAIL>",
                    City = "جدة",
                    CurrentBalance = -8500,
                    LastTransactionDate = DateTime.Today.AddDays(-1),
                    IsActive = true
                },
                new SupplierViewModel
                {
                    SupplierNumber = "SUP003",
                    Name = "شركة المواد الخام",
                    TradeName = "Raw Materials Company",
                    Phone = "013-2345678",
                    Email = "<EMAIL>",
                    City = "الدمام",
                    CurrentBalance = -22000,
                    LastTransactionDate = DateTime.Today.AddDays(-5),
                    IsActive = true
                },
                new SupplierViewModel
                {
                    SupplierNumber = "SUP004",
                    Name = "مكتب الخدمات التقنية",
                    TradeName = "Tech Services Office",
                    Phone = "014-1234567",
                    Email = "<EMAIL>",
                    City = "الرياض",
                    CurrentBalance = 0,
                    LastTransactionDate = DateTime.Today.AddDays(-10),
                    IsActive = true
                },
                new SupplierViewModel
                {
                    SupplierNumber = "SUP005",
                    Name = "شركة المعدات المكتبية",
                    TradeName = "Office Equipment Co.",
                    Phone = "015-9876543",
                    Email = "<EMAIL>",
                    City = "مكة المكرمة",
                    CurrentBalance = -5200,
                    LastTransactionDate = DateTime.Today.AddDays(-15),
                    IsActive = false
                }
            };

            SuppliersDataGrid.ItemsSource = _suppliers;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var visibleSuppliers = SuppliersDataGrid.ItemsSource as ObservableCollection<SupplierViewModel> ?? _suppliers;
            
            SupplierCountTextBlock.Text = $"عدد الموردين: {visibleSuppliers.Count}";
            
            var totalBalance = visibleSuppliers.Sum(s => s.CurrentBalance);
            TotalBalanceTextBlock.Text = $"إجمالي الأرصدة: {totalBalance:C}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void AddSupplier_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddSupplierWindow();
            if (addWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم إضافة المورد بنجاح";
            }
        }

        private void EditSupplier_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSupplier == null)
            {
                MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editWindow = new AddSupplierWindow(_selectedSupplier);
            if (editWindow.ShowDialog() == true)
            {
                RefreshButton_Click(sender, e);
                StatusTextBlock.Text = "تم تعديل المورد بنجاح";
            }
        }

        private void DeleteSupplier_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSupplier == null)
            {
                MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_selectedSupplier.CurrentBalance != 0)
            {
                MessageBox.Show("لا يمكن حذف مورد له رصيد مستحق", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف المورد '{_selectedSupplier.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _suppliers.Remove(_selectedSupplier);
                UpdateSummary();
                StatusTextBlock.Text = "تم حذف المورد بنجاح";
            }
        }

        private void PurchaseInvoices_Click(object sender, RoutedEventArgs e)
        {
            var purchaseInvoicesWindow = new PurchaseInvoicesWindow();
            purchaseInvoicesWindow.ShowDialog();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            StatusTextBlock.Text = "تم تحديث البيانات";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            if (_suppliers == null) return;

            var selectedStatus = StatusFilterComboBox.SelectedItem?.ToString() ?? "جميع الموردين";
            var searchText = SearchTextBox.Text?.ToLower() ?? "";

            var filteredSuppliers = _suppliers.Where(s => 
                (selectedStatus == "جميع الموردين" || 
                 (selectedStatus == "نشط" && s.IsActive) ||
                 (selectedStatus == "غير نشط" && !s.IsActive)) &&
                (string.IsNullOrWhiteSpace(searchText) || 
                 s.Name.ToLower().Contains(searchText) || 
                 s.TradeName.ToLower().Contains(searchText) ||
                 s.SupplierNumber.ToLower().Contains(searchText) ||
                 s.Phone.Contains(searchText) ||
                 s.Email.ToLower().Contains(searchText) ||
                 s.City.Contains(searchText))
            ).ToList();
            
            SuppliersDataGrid.ItemsSource = new ObservableCollection<SupplierViewModel>(filteredSuppliers);
            UpdateSummary();
        }

        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedSupplier = SuppliersDataGrid.SelectedItem as SupplierViewModel;
            
            if (_selectedSupplier != null)
            {
                StatusTextBlock.Text = $"تم اختيار: {_selectedSupplier.SupplierNumber} - {_selectedSupplier.Name}";
            }
        }
    }

    public class SupplierViewModel
    {
        public string SupplierNumber { get; set; } = "";
        public string Name { get; set; } = "";
        public string TradeName { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string City { get; set; } = "";
        public decimal CurrentBalance { get; set; }
        public DateTime LastTransactionDate { get; set; }
        public bool IsActive { get; set; }
        
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "Green" : "Red";
        public string BalanceColor => CurrentBalance < 0 ? "Red" : CurrentBalance > 0 ? "Green" : "Gray";
    }
}
